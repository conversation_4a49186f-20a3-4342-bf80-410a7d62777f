package you

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
)

// modelMap OpenAI模型名称到You.com模型名称的映射表
// 支持多种主流AI模型的转换
var modelMap = map[string]string{
	"deepseek-reasoner":       "deepseek_r1",
	"deepseek-chat":           "deepseek_v3",
	"o3-mini-high":            "openai_o3_mini_high",
	"o1":                      "openai_o1",
	"gpt5":                     "gpt_5",
	"gpt5-mini":               "gpt_5_mini",
	"gpt-4o":                  "gpt_4o",
	"gpt-4o-mini":             "gpt_4o_mini",
	"claude-3-opus":           "claude_3_opus",
	"claude-3.5-sonnet":       "claude_3_5_sonnet",
	"gemini-1.5-pro":          "gemini_1_5_pro",
	"gemini-2.0-flash":        "gemini_2_flash",
	"llama-3.3-70b":           "llama3_3_70b",
	"llama-4-maverick":        "llama4_maverick",
	"llama-4-scout":           "llama4_scout",
	"mistral-large-2":         "mistral_large_2",
	"qwen3-235b":              "qwen3_235b",
	"qwq-32b":                 "qwq_32b",
	"qwen-2.5-72b":            "qwen2p5_72b",
	"qwen-2.5-coder-32b":      "qwen2p5_coder_32b",
	"command-r-plus":          "command_r_plus",
	"claude-3-7-sonnet":       "claude_3_7_sonnet",
	"claude-3-7-sonnet-think": "claude_3_7_sonnet_thinking",
	"claude-4-sonnet":         "claude_4_sonnet",
	"claude-4-sonnet-think":   "claude_4_sonnet_thinking",
	"claude-4-opus":           "claude_4_opus",
	"claude-4-opus-think":     "claude_4_opus_thinking",
	"gemini-2.5-pro":          "gemini_2_5_pro_preview",
	"o3":                      "openai_o3",
	"o3-pro":                  "openai_o3_pro",
	"o4-mini-high":            "openai_o4_mini_high",
	"gpt-4.1":                 "gpt_4_1",
	"grok-4":                  "grok_4",
	"grok-3-beta":             "grok_3",
	"grok-3-mini":             "grok_3_mini",
	"grok-2":                  "grok_2",
	"nous-hermes-2":           "nous_hermes_2",
}

// mapModelName 映射OpenAI模型名到You.com模型名
func mapModelName(openAIModel string) string {
	if mappedModel, exists := modelMap[openAIModel]; exists {
		return mappedModel
	}
	return "deepseek_v3" // 默认模型
}

// isAgentModel 检查是否为Agent模型
func (h *Handler) isAgentModel(modelID string) bool {
	for _, id := range h.config.AgentModelIDs {
		if id == modelID {
			return true
		}
	}
	return false
}

// buildYouRequest 构建You.com API请求
func (h *Handler) buildYouRequest(req OpenAIRequest, dsToken string) (*http.Request, error) {
	h.logger.Debug(fmt.Sprintf("Building You.com request for model: %s", req.Model))

	// 转换system消息
	req.Messages = h.convertSystemToUserMessages(req.Messages)
	h.logger.Debug(fmt.Sprintf("Converted messages count: %d", len(req.Messages)))

	// 根据配置选择请求方式
	if h.config.UsePostMethod {
		h.logger.Debug("Using POST method for You.com request")
		// POST方式：不处理长消息上传，直接构建聊天历史
		chatHistory, sources := h.buildChatHistoryForPost(req.Messages, dsToken)
		return h.buildPostRequest(req, chatHistory, sources, dsToken)
	} else {
		h.logger.Debug("Using GET method for You.com request")
		// GET方式：需要处理长消息上传和URL优化
		chatHistory, sources := h.buildChatHistory(req.Messages, dsToken)
		// 检查聊天历史JSON长度，如果太长则进行优化
		chatHistory, sources = h.optimizeChatHistoryForURL(chatHistory, sources, dsToken)
		chatHistoryJSON, _ := json.Marshal(chatHistory)
		return h.buildGetRequest(req, chatHistory, sources, dsToken, chatHistoryJSON)
	}
}

// buildGetRequest 构建GET请求（原有逻辑）
func (h *Handler) buildGetRequest(req OpenAIRequest, chatHistory []ChatEntry, sources []map[string]interface{}, dsToken string, chatHistoryJSON []byte) (*http.Request, error) {
	// 处理最后一条消息
	lastMessage := req.Messages[len(req.Messages)-1]
	finalQuery, imageSources := h.processMessageContent(lastMessage.Content, dsToken)

	// 检查最后一条消息是否过长，如果是则上传为文件
	lastMessageTokens, err := h.countTokensForMessages([]Message{lastMessage})
	if err == nil && lastMessageTokens > h.config.MaxContextToken {
		h.logger.Debug(fmt.Sprintf("Last message too long (%d tokens > %d), uploading as file",
			lastMessageTokens, h.config.MaxContextToken))

		if uploadedMessage, err := h.uploadTextAsFile(finalQuery, dsToken); err == nil {
			// 添加文件源信息
			sources = append(sources, map[string]interface{}{
				"source_type":   "user_file",
				"filename":      uploadedMessage.Filename,
				"user_filename": uploadedMessage.UserFilename,
				"size_bytes":    len(finalQuery),
			})

			// 使用文件引用作为查询，确保包含.txt后缀
			finalQuery = fmt.Sprintf("查看这个文件并且直接与文件内容进行聊天：%s.txt",
				strings.TrimSuffix(uploadedMessage.UserFilename, ".txt"))
		} else {
			h.logger.Error(fmt.Sprintf("Failed to upload last message as file: %v", err))
		}
	}

	// 合并sources
	if imageSources != "" {
		var imageSourcesList []map[string]interface{}
		if err := json.Unmarshal([]byte(imageSources), &imageSourcesList); err == nil {
			sources = append(sources, imageSourcesList...)
		}
	}

	// 创建You.com请求
	youReq, err := http.NewRequest("GET", "https://you.com/api/streamingSearch", nil)
	if err != nil {
		return nil, err
	}

	// 生成必要ID
	chatId := uuid.New().String()
	conversationTurnId := uuid.New().String()
	traceId := fmt.Sprintf("%s|%s|%s", chatId, conversationTurnId, time.Now().Format(time.RFC3339))

	// 构建查询参数
	q := youReq.URL.Query()
	q.Add("page", "1")
	q.Add("count", "10")
	q.Add("safeSearch", "Moderate")
	q.Add("mkt", "zh-HK")
	q.Add("enable_worklow_generation_ux", "true")
	q.Add("domain", "youchat")
	q.Add("use_personalization_extraction", "true")
	q.Add("queryTraceId", chatId)
	q.Add("chatId", chatId)
	q.Add("conversationTurnId", conversationTurnId)
	q.Add("pastChatLength", fmt.Sprintf("%d", len(chatHistory)))
	q.Add("enable_agent_clarification_questions", "true")
	q.Add("traceId", traceId)
	q.Add("use_nested_youchat_updates", "true")

	// 根据模型类型设置参数
	if h.isAgentModel(req.Model) {
		h.logger.Debug(fmt.Sprintf("Using agent model: %s", req.Model))
		q.Add("selectedChatMode", req.Model)
	} else {
		mappedModel := mapModelName(req.Model)
		h.logger.Debug(fmt.Sprintf("Using mapped model: %s -> %s", req.Model, mappedModel))
		q.Add("selectedAiModel", mappedModel)
		q.Add("selectedChatMode", "custom")
	}

	// 添加sources
	if len(sources) > 0 {
		sourcesJSON, _ := json.Marshal(sources)
		q.Add("sources", string(sourcesJSON))
	}

	q.Add("q", finalQuery)
	q.Add("chat", string(chatHistoryJSON))
	youReq.URL.RawQuery = q.Encode()

	h.logger.Debug(fmt.Sprintf("Built You.com GET request URL: %s", youReq.URL.String()))

	// 设置请求头
	youReq.Header = http.Header{
		"sec-ch-ua-platform":         {"Windows"},
		"Cache-Control":              {"no-cache"},
		"sec-ch-ua":                  {`"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"`},
		"sec-ch-ua-bitness":          {"64"},
		"sec-ch-ua-model":            {""},
		"sec-ch-ua-mobile":           {"?0"},
		"sec-ch-ua-arch":             {"x86"},
		"sec-ch-ua-full-version":     {"133.0.3065.39"},
		"Accept":                     {"text/event-stream"},
		"User-Agent":                 {"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},
		"sec-ch-ua-platform-version": {"19.0.0"},
		"Sec-Fetch-Site":             {"same-origin"},
		"Sec-Fetch-Mode":             {"cors"},
		"Sec-Fetch-Dest":             {"empty"},
		"Host":                       {"you.com"},
	}

	// 设置Cookie
	cookies := h.getCookies(dsToken)
	var cookieStrings []string
	for name, value := range cookies {
		cookieStrings = append(cookieStrings, fmt.Sprintf("%s=%s", name, value))
	}
	youReq.Header.Add("Cookie", strings.Join(cookieStrings, ";"))

	return youReq, nil
}

// buildPostRequest 构建POST请求（新的方式）
func (h *Handler) buildPostRequest(req OpenAIRequest, chatHistory []ChatEntry, sources []map[string]interface{}, dsToken string) (*http.Request, error) {
	// 处理最后一条消息
	lastMessage := req.Messages[len(req.Messages)-1]
	finalQuery, imageSources := h.processMessageContent(lastMessage.Content, dsToken)

	// POST方式不需要处理长消息上传为文件，因为数据在请求体中，无URL长度限制
	h.logger.Debug("POST method: skipping file upload optimization for long messages")

	// 合并sources（仅处理图片sources）
	if imageSources != "" {
		var imageSourcesList []map[string]interface{}
		if err := json.Unmarshal([]byte(imageSources), &imageSourcesList); err == nil {
			sources = append(sources, imageSourcesList...)
		}
	}

	// 生成必要ID
	chatId := uuid.New().String()
	conversationTurnId := uuid.New().String()
	traceId := fmt.Sprintf("%s|%s|%s", chatId, conversationTurnId, time.Now().Format(time.RFC3339))

	// 构建POST请求体
	chatHistoryJSON, _ := json.Marshal(chatHistory)
	postBody := map[string]interface{}{
		"query": finalQuery,
		"chat":  string(chatHistoryJSON),
	}
	postBodyJSON, err := json.Marshal(postBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal POST body: %v", err)
	}

	// 创建You.com POST请求
	youReq, err := http.NewRequest("POST", "https://you.com/api/streamingSearch", bytes.NewBuffer(postBodyJSON))
	if err != nil {
		return nil, err
	}

	// 构建查询参数（基于curl请求）
	q := youReq.URL.Query()
	q.Add("page", "1")
	q.Add("count", "10")
	q.Add("safeSearch", "Moderate")
	q.Add("mkt", "en-US")
	q.Add("enable_worklow_generation_ux", "true")
	q.Add("domain", "youchat")
	q.Add("use_personalization_extraction", "true")
	q.Add("queryTraceId", chatId)
	q.Add("chatId", chatId)
	q.Add("conversationTurnId", conversationTurnId)
	q.Add("pastChatLength", fmt.Sprintf("%d", len(chatHistory)))
	q.Add("enable_editable_workflow", "true")
	q.Add("use_nested_youchat_updates", "true")
	q.Add("enable_agent_clarification_questions", "true")
	q.Add("traceId", traceId)

	// 根据模型类型设置参数
	if h.isAgentModel(req.Model) {
		h.logger.Debug(fmt.Sprintf("Using agent model: %s", req.Model))
		q.Add("selectedChatMode", req.Model)
	} else {
		mappedModel := mapModelName(req.Model)
		h.logger.Debug(fmt.Sprintf("Using mapped model: %s -> %s", req.Model, mappedModel))
		q.Add("selectedAiModel", mappedModel)
		q.Add("selectedChatMode", "custom")
	}

	// 添加sources（如果有的话）
	if len(sources) > 0 {
		sourcesJSON, _ := json.Marshal(sources)
		q.Add("sources", string(sourcesJSON))
		h.logger.Debug(fmt.Sprintf("Added sources to POST request: %s", string(sourcesJSON)))
	}

	youReq.URL.RawQuery = q.Encode()

	h.logger.Debug(fmt.Sprintf("Built You.com POST request URL: %s", youReq.URL.String()))
	h.logger.Debug(fmt.Sprintf("POST body: %s", string(postBodyJSON)))

	// 设置请求头（基于curl请求）
	youReq.Header = http.Header{
		"Accept":                     {"*/*"},
		"Accept-Language":            {"en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,ja;q=0.6"},
		"Cache-Control":              {"no-cache"},
		"Connection":                 {"keep-alive"},
		"Content-Type":               {"text/plain;charset=UTF-8"},
		"DNT":                        {"1"},
		"Origin":                     {"https://you.com"},
		"Pragma":                     {"no-cache"},
		"Referer":                    {"https://you.com/"},
		"Sec-Fetch-Dest":             {"empty"},
		"Sec-Fetch-Mode":             {"cors"},
		"Sec-Fetch-Site":             {"same-origin"},
		"User-Agent":                 {"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
		"sec-ch-ua":                  {`"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`},
		"sec-ch-ua-arch":             {`"x86"`},
		"sec-ch-ua-bitness":          {`"64"`},
		"sec-ch-ua-full-version":     {`"137.0.7151.104"`},
		"sec-ch-ua-mobile":           {"?0"},
		"sec-ch-ua-model":            {`""`},
		"sec-ch-ua-platform":         {`"Windows"`},
		"sec-ch-ua-platform-version": {`"19.0.0"`},
		"Host":                       {"you.com"},
	}

	// 设置Cookie
	cookies := h.getCookies(dsToken)
	var cookieStrings []string
	for name, value := range cookies {
		cookieStrings = append(cookieStrings, fmt.Sprintf("%s=%s", name, value))
	}
	youReq.Header.Add("Cookie", strings.Join(cookieStrings, ";"))

	return youReq, nil
}

// getCookies 获取Cookie
func (h *Handler) getCookies(dsToken string) map[string]string {
	return map[string]string{
		"guest_has_seen_legal_disclaimer": "true",
		"youchat_personalization":         "true",
		"DS":                              dsToken,
		"you_subscription":                "youpro_standard_year",
		"youpro_subscription":             "true",
		"ai_model":                        "deepseek_r1",
		"youchat_smart_learn":             "true",
	}
}

// convertSystemToUserMessages 转换system消息
func (h *Handler) convertSystemToUserMessages(messages []Message) []Message {
	if len(messages) == 0 {
		return messages
	}

	var systemContent strings.Builder
	var newMessages []Message
	var systemFound bool

	// 收集所有system消息
	for _, msg := range messages {
		if msg.Role == "system" {
			if systemContent.Len() > 0 {
				systemContent.WriteString("\n")
			}
			systemContent.WriteString(h.extractTextContent(msg.Content))
			systemFound = true
		} else {
			newMessages = append(newMessages, msg)
		}
	}

	// 如果有system消息，将其作为第一条user消息
	if systemFound {
		newMessages = append([]Message{{
			Role:    "user",
			Content: systemContent.String(),
		}}, newMessages...)
	}

	return newMessages
}

// buildChatHistory 构建聊天历史
func (h *Handler) buildChatHistory(messages []Message, dsToken string) ([]ChatEntry, []map[string]interface{}) {
	return h.buildChatHistoryWithOptions(messages, dsToken, true)
}

// buildChatHistoryForPost 构建POST请求的聊天历史（不处理长消息上传）
func (h *Handler) buildChatHistoryForPost(messages []Message, dsToken string) ([]ChatEntry, []map[string]interface{}) {
	return h.buildChatHistoryWithOptions(messages, dsToken, false)
}

// buildChatHistoryWithOptions 构建聊天历史，可选择是否处理长消息上传
func (h *Handler) buildChatHistoryWithOptions(messages []Message, dsToken string, processLongMessages bool) ([]ChatEntry, []map[string]interface{}) {
	var chatHistory []ChatEntry
	var sources []map[string]interface{}

	// 处理历史消息（不包括最后一条）
	var currentQuestion string
	var currentAnswer string
	var hasQuestion bool
	var hasAnswer bool

	for i := 0; i < len(messages)-1; i++ {
		msg := messages[i]

		if msg.Role == "user" {
			if hasQuestion && hasAnswer {
				chatHistory = append(chatHistory, ChatEntry{
					Question: currentQuestion,
					Answer:   currentAnswer,
				})
				currentQuestion = h.extractTextContent(msg.Content)
				currentAnswer = ""
				hasQuestion = true
				hasAnswer = false
			} else if hasQuestion {
				currentQuestion += "\n" + h.extractTextContent(msg.Content)
			} else {
				currentQuestion = h.extractTextContent(msg.Content)
				hasQuestion = true
			}
		} else if msg.Role == "assistant" {
			if hasQuestion {
				currentAnswer = h.extractTextContent(msg.Content)
				hasAnswer = true
			} else if hasAnswer {
				currentAnswer += "\n" + h.extractTextContent(msg.Content)
			} else {
				currentQuestion = ""
				currentAnswer = h.extractTextContent(msg.Content)
				hasQuestion = true
				hasAnswer = true
			}
		}
	}

	// 添加最后一对问答
	if hasQuestion {
		if hasAnswer {
			chatHistory = append(chatHistory, ChatEntry{
				Question: currentQuestion,
				Answer:   currentAnswer,
			})
		} else {
			chatHistory = append(chatHistory, ChatEntry{
				Question: currentQuestion,
				Answer:   "",
			})
		}
	}

	// 根据选项决定是否处理聊天历史中的长消息
	if processLongMessages {
		h.logger.Debug("Processing long messages in chat history for GET request")
		h.processLongChatHistory(chatHistory, &sources, dsToken)
	} else {
		h.logger.Debug("Skipping long message processing for POST request")
	}

	return chatHistory, sources
}

// optimizeChatHistoryForURL 优化聊天历史以避免URL过长
func (h *Handler) optimizeChatHistoryForURL(chatHistory []ChatEntry, sources []map[string]interface{}, dsToken string) ([]ChatEntry, []map[string]interface{}) {
	const maxURLLength = 6000 // URL中聊天历史部分的最大长度，留余量给其他参数

	// 检查当前聊天历史JSON的长度
	chatHistoryJSON, _ := json.Marshal(chatHistory)
	currentLength := len(string(chatHistoryJSON))

	h.logger.Debug(fmt.Sprintf("Current chat history JSON length: %d characters", currentLength))

	if currentLength <= maxURLLength {
		h.logger.Debug("Chat history length is acceptable, no optimization needed")
		return chatHistory, sources
	}

	h.logger.Warning(fmt.Sprintf("Chat history too long (%d chars), uploading as file to avoid 414 error", currentLength))

	// 直接使用文件上传策略
	if dsToken != "" {
		return h.uploadChatHistoryAsFile(chatHistory, sources, dsToken)
	}

	// 如果没有dsToken，使用截断策略作为备用
	h.logger.Warning("No DS token available, using truncated history as fallback")
	optimizedHistory := h.truncateChatHistory(chatHistory, maxURLLength)
	return optimizedHistory, sources
}

// truncateChatHistory 截断聊天历史，保留最近的对话
func (h *Handler) truncateChatHistory(chatHistory []ChatEntry, maxLength int) []ChatEntry {
	if len(chatHistory) <= 1 {
		return chatHistory
	}

	// 从最后开始，逐步添加历史记录，直到接近长度限制
	var result []ChatEntry
	currentLength := 2 // 起始的 "[]" 长度

	// 从后往前遍历，保留最近的对话
	for i := len(chatHistory) - 1; i >= 0; i-- {
		entry := chatHistory[i]
		entryJSON, _ := json.Marshal(entry)
		entryLength := len(string(entryJSON))

		// 如果添加这个条目会超过限制，就停止
		if currentLength+entryLength+1 > maxLength { // +1 for comma
			break
		}

		result = append([]ChatEntry{entry}, result...) // 前插
		currentLength += entryLength + 1 // +1 for comma
	}

	h.logger.Debug(fmt.Sprintf("Truncated chat history from %d to %d entries", len(chatHistory), len(result)))
	return result
}

// uploadChatHistoryAsFile 将聊天历史上传为文件
func (h *Handler) uploadChatHistoryAsFile(chatHistory []ChatEntry, sources []map[string]interface{}, dsToken string) ([]ChatEntry, []map[string]interface{}) {
	// 构建聊天历史的文本表示
	var historyText strings.Builder
	historyText.WriteString("聊天历史记录：\n\n")

	for i, entry := range chatHistory {
		historyText.WriteString(fmt.Sprintf("=== 对话 %d ===\n", i+1))
		if entry.Question != "" {
			historyText.WriteString(fmt.Sprintf("用户: %s\n", entry.Question))
		}
		if entry.Answer != "" {
			historyText.WriteString(fmt.Sprintf("助手: %s\n", entry.Answer))
		}
		historyText.WriteString("\n")
	}

	// 上传聊天历史文件
	uploadResp, err := h.uploadTextAsFile(historyText.String(), dsToken)
	if err != nil {
		h.logger.Error(fmt.Sprintf("Failed to upload chat history as file: %v", err))
		// 如果上传失败，返回截断的历史
		return h.truncateChatHistory(chatHistory, 6000), sources
	}

	h.logger.Info(fmt.Sprintf("Successfully uploaded chat history as file: %s", uploadResp.UserFilename))

	// 添加文件源信息
	newSources := append(sources, map[string]interface{}{
		"source_type":   "chat_history_file",
		"filename":      uploadResp.Filename,
		"user_filename": uploadResp.UserFilename,
		"size_bytes":    len(historyText.String()),
	})

	// 返回一个简化的聊天历史，只包含对文件的引用
	simplifiedHistory := []ChatEntry{
		{
			Question: fmt.Sprintf("请查看这个聊天历史文件：%s", uploadResp.UserFilename),
			Answer:   "我已经查看了聊天历史文件，可以基于之前的对话内容继续为您服务。",
		},
	}

	return simplifiedHistory, newSources
}

// processLongChatHistory 处理聊天历史中的长消息，上传为文件
func (h *Handler) processLongChatHistory(chatHistory []ChatEntry, sources *[]map[string]interface{}, dsToken string) {
	if dsToken == "" {
		h.logger.Debug("DS token not available, skipping long message processing")
		return
	}

	const tokenThreshold = 30 // 历史消息的token阈值

	for i := range chatHistory {
		entry := &chatHistory[i]

		// 处理问题
		if entry.Question != "" {
			questionTokenCount, _ := h.countTokensForMessages([]Message{{Role: "user", Content: entry.Question}})

			// 如果问题较长，上传为文件
			if questionTokenCount >= tokenThreshold {
				h.logger.Debug(fmt.Sprintf("Question too long (%d tokens), uploading as file", questionTokenCount))

				if uploadedQuestion, err := h.uploadTextAsFile(entry.Question, dsToken); err == nil {
					// 添加问题文件源信息
					*sources = append(*sources, map[string]interface{}{
						"source_type":   "user_file",
						"filename":      uploadedQuestion.Filename,
						"user_filename": uploadedQuestion.UserFilename,
						"size_bytes":    len(entry.Question),
					})

					// 更新问题为文件引用
					entry.Question = fmt.Sprintf("查看这个文件并且直接与文件内容进行聊天：%s.txt",
						strings.TrimSuffix(uploadedQuestion.UserFilename, ".txt"))
				} else {
					h.logger.Error(fmt.Sprintf("Failed to upload question as file: %v", err))
				}
			}
		}

		// 处理回答 - 只对长回答进行文件上传
		if entry.Answer != "" {
			answerTokenCount, _ := h.countTokensForMessages([]Message{{Role: "assistant", Content: entry.Answer}})

			// 如果回答较长，上传为文件
			if answerTokenCount >= tokenThreshold {
				h.logger.Debug(fmt.Sprintf("Answer too long (%d tokens), uploading as file", answerTokenCount))

				if uploadedAnswer, err := h.uploadTextAsFile(entry.Answer, dsToken); err == nil {
					// 添加回答文件源信息
					*sources = append(*sources, map[string]interface{}{
						"source_type":   "user_file",
						"filename":      uploadedAnswer.Filename,
						"user_filename": uploadedAnswer.UserFilename,
						"size_bytes":    len(entry.Answer),
					})

					// 更新回答为文件引用
					entry.Answer = fmt.Sprintf("查看这个文件并且直接与文件内容进行聊天：%s.txt",
						strings.TrimSuffix(uploadedAnswer.UserFilename, ".txt"))
				} else {
					h.logger.Error(fmt.Sprintf("Failed to upload answer as file: %v", err))
				}
			}
		}
	}
}

// uploadTextAsFile 将文本内容上传为文件
func (h *Handler) uploadTextAsFile(content, dsToken string) (*UploadResponse, error) {
	// 获取nonce
	_, err := h.getNonce(dsToken)
	if err != nil {
		return nil, fmt.Errorf("failed to get nonce: %v", err)
	}

	// 生成文件名和准备数据
	shortFileName := h.generateShortFileName()
	filename := shortFileName + ".txt"
	data := h.addUTF8BOM(content)

	// 直接从内存上传数据
	uploadResp, err := h.uploadDataAsFile(data, filename, dsToken)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %v", err)
	}

	return uploadResp, nil
}

// processMessageContent 处理消息内容
func (h *Handler) processMessageContent(content interface{}, dsToken string) (string, string) {
	textContent := h.extractTextContent(content)
	
	if !h.hasImageContent(content) {
		return textContent, ""
	}
	
	var sources []map[string]interface{}
	
	switch v := content.(type) {
	case []interface{}:
		for _, part := range v {
			if partMap, ok := part.(map[string]interface{}); ok {
				if partType, exists := partMap["type"]; exists && partType == "image_url" {
					if imageURL, exists := partMap["image_url"]; exists {
						if imageURLMap, ok := imageURL.(map[string]interface{}); ok {
							if url, exists := imageURLMap["url"]; exists {
								if urlStr, ok := url.(string); ok {
									filename, userFilename, err := h.processImage(urlStr, dsToken)
									if err != nil {
										continue
									}
									
									source := map[string]interface{}{
										"filename":      filename,
										"size_bytes":    0,
										"source_type":   "user_file",
										"user_filename": userFilename,
									}
									sources = append(sources, source)
									
									textContent += fmt.Sprintf("\n查看这个图片文件：%s", userFilename)
								}
							}
						}
					}
				}
			}
		}
	}
	
	sourcesJSON := ""
	if len(sources) > 0 {
		if sourcesBytes, err := json.Marshal(sources); err == nil {
			sourcesJSON = string(sourcesBytes)
		}
	}
	
	return textContent, sourcesJSON
}

// extractTextContent 提取文本内容
func (h *Handler) extractTextContent(content interface{}) string {
	switch v := content.(type) {
	case string:
		return v
	case []interface{}:
		var textParts []string
		for _, part := range v {
			if partMap, ok := part.(map[string]interface{}); ok {
				if partType, exists := partMap["type"]; exists && partType == "text" {
					if text, exists := partMap["text"]; exists {
						if textStr, ok := text.(string); ok {
							textParts = append(textParts, textStr)
						}
					}
				}
			}
		}
		return strings.Join(textParts, " ")
	default:
		return ""
	}
}

// hasImageContent 检查是否包含图片内容
func (h *Handler) hasImageContent(content interface{}) bool {
	switch v := content.(type) {
	case []interface{}:
		for _, part := range v {
			if partMap, ok := part.(map[string]interface{}); ok {
				if partType, exists := partMap["type"]; exists && partType == "image_url" {
					return true
				}
			}
		}
	}
	return false
}

// processImage 处理图片
func (h *Handler) processImage(imageURL, dsToken string) (string, string, error) {
	var imageData []byte
	var err error
	var originalFilename string
	
	if strings.HasPrefix(imageURL, "data:image/") {
		imageData, originalFilename, err = h.decodeBase64Image(imageURL)
	} else {
		imageData, originalFilename, err = h.downloadImage(imageURL)
	}
	
	if err != nil {
		return "", "", err
	}
	
	// 直接从内存上传图片数据
	uploadResp, err := h.uploadDataAsFile(imageData, originalFilename, dsToken)
	if err != nil {
		return "", "", err
	}
	
	return uploadResp.Filename, uploadResp.UserFilename, nil
}

// decodeBase64Image 解码base64图片
func (h *Handler) decodeBase64Image(dataURL string) ([]byte, string, error) {
	parts := strings.Split(dataURL, ",")
	if len(parts) != 2 {
		return nil, "", fmt.Errorf("invalid data URL format")
	}
	
	header := parts[0]
	var extension string
	if strings.Contains(header, "image/png") {
		extension = ".png"
	} else if strings.Contains(header, "image/jpeg") || strings.Contains(header, "image/jpg") {
		extension = ".jpg"
	} else if strings.Contains(header, "image/gif") {
		extension = ".gif"
	} else if strings.Contains(header, "image/webp") {
		extension = ".webp"
	} else {
		extension = ".png"
	}
	
	imageData, err := base64.StdEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, "", fmt.Errorf("base64 decode failed: %v", err)
	}
	
	filename := fmt.Sprintf("%s_image%s", h.generateRandomString(6), extension)
	return imageData, filename, nil
}

// downloadImage 下载图片
func (h *Handler) downloadImage(imageURL string) ([]byte, string, error) {
	// 使用配置的HTTP客户端
	client := h.httpClient
	
	// 验证URL格式
	if _, err := url.Parse(imageURL); err != nil {
		return nil, "", fmt.Errorf("invalid image URL: %v", err)
	}
	
	resp, err := client.Get(imageURL)
	if err != nil {
		return nil, "", fmt.Errorf("download failed: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, "", fmt.Errorf("download failed, status: %d", resp.StatusCode)
	}
	
	// 限制图片大小（10MB）
	limitReader := io.LimitReader(resp.Body, 10*1024*1024)
	imageData, err := io.ReadAll(limitReader)
	if err != nil {
		return nil, "", fmt.Errorf("read failed: %v", err)
	}
	
	filename := filepath.Base(imageURL)
	if filename == "." || filename == "/" || filename == "" {
		filename = "downloaded_image.jpg"
	}
	
	return imageData, filename, nil
}

// generateRandomString 生成随机字符串
func (h *Handler) generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// uploadDataAsFile 直接从内存上传数据作为文件
func (h *Handler) uploadDataAsFile(data []byte, filename string, dsToken string) (*UploadResponse, error) {
	// 检查数据大小（限制为10MB）
	if len(data) > 10*1024*1024 {
		return nil, fmt.Errorf("data too large: %d bytes (max 10MB)", len(data))
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %v", err)
	}

	if _, err := part.Write(data); err != nil {
		return nil, fmt.Errorf("failed to write data: %v", err)
	}
	writer.Close()

	req, err := http.NewRequest("POST", "https://you.com/api/upload", body)
	if err != nil {
		return nil, fmt.Errorf("failed to create upload request: %v", err)
	}

	// 设置完整的请求头，模拟浏览器行为
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Cookie", fmt.Sprintf("DS=%s", dsToken))
	req.Header.Set("Accept", "multipart/form-data")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,ja;q=0.6")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("DNT", "1")
	req.Header.Set("Origin", "https://you.com")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Referer", "https://you.com/")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	req.Header.Set("sec-ch-ua", `"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`)
	req.Header.Set("sec-ch-ua-arch", "x86")
	req.Header.Set("sec-ch-ua-bitness", "64")
	req.Header.Set("sec-ch-ua-full-version", "137.0.7151.104")
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("sec-ch-ua-model", "")
	req.Header.Set("sec-ch-ua-platform", "Windows")
	req.Header.Set("sec-ch-ua-platform-version", "19.0.0")
	req.Header.Set("Host", "you.com")

	// 使用配置的HTTP客户端
	client := h.httpClient

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send upload request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("upload failed, status: %d, response: %s", resp.StatusCode, string(body))
	}

	var uploadResp UploadResponse
	if err := json.NewDecoder(resp.Body).Decode(&uploadResp); err != nil {
		return nil, fmt.Errorf("failed to decode upload response: %v", err)
	}

	h.logger.Debug(fmt.Sprintf("Data uploaded successfully as file: %s -> %s", uploadResp.UserFilename, uploadResp.Filename))
	return &uploadResp, nil
}

// uploadFile 上传文件（保留用于兼容性，内部调用uploadDataAsFile）
func (h *Handler) uploadFile(dsToken, filePath string) (*UploadResponse, error) {
	// 读取文件数据到内存
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %v", err)
	}

	// 获取文件名
	filename := filepath.Base(filePath)

	// 调用内存版本的上传函数
	return h.uploadDataAsFile(data, filename, dsToken)
}

// countTokens 计算消息内容的token数量，包括图片token估算
func (h *Handler) countTokens(content interface{}) int {
	textContent := h.extractTextContent(content)
	baseTokens := len(strings.Fields(textContent)) // 简单的单词计数估算

	// 如果包含图片，每张图片估算85个token
	if h.hasImageContent(content) {
		imageCount := 0
		if v, ok := content.([]interface{}); ok {
			for _, part := range v {
				if partMap, ok := part.(map[string]interface{}); ok {
					if partType, exists := partMap["type"]; exists && partType == "image_url" {
						imageCount++
					}
				}
			}
		}
		baseTokens += imageCount * 85
	}

	return baseTokens
}

// countTokensForMessages 计算消息的token数（使用字符估算方法），支持多模态内容
func (h *Handler) countTokensForMessages(messages []Message) (int, error) {
	totalTokens := 0
	for _, msg := range messages {
		tokens := h.countTokens(msg.Content)
		totalTokens += tokens + 2 // 加上角色名的token（约2个）
	}
	return totalTokens, nil
}

// generateShortFileName 生成短文件名
func (h *Handler) generateShortFileName() string {
	// 生成6位纯英文字母字符串
	const charset = "abcdefghijklmnopqrstuvwxyz"
	result := make([]byte, 6)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}

// addUTF8BOM 添加UTF-8 BOM标记的函数
func (h *Handler) addUTF8BOM(content string) []byte {
	// 首先确保内容是纯文本
	content = h.ensurePlainText(content)
	// UTF-8 BOM: EF BB BF
	bom := []byte{0xEF, 0xBB, 0xBF}
	return append(bom, []byte(content)...)
}

// ensurePlainText 确保内容为纯文本
func (h *Handler) ensurePlainText(content string) string {
	// 移除可能导致问题的不可打印字符
	var result strings.Builder
	for _, r := range content {
		// 只保留ASCII可打印字符、基本中文字符和基本标点符号
		if (r >= 32 && r <= 126) || // ASCII可打印字符
			(r >= 0x4E00 && r <= 0x9FA5) || // 基本汉字
			(r >= 0x3000 && r <= 0x303F) || // 中文标点
			r == 0x000A || r == 0x000D { // 换行和回车
			result.WriteRune(r)
		} else {
			// 替换其他字符为空格
			result.WriteRune(' ')
		}
	}
	return result.String()
}

// getNonce 获取上传文件所需的nonce
func (h *Handler) getNonce(dsToken string) (*NonceResponse, error) {
	req, _ := http.NewRequest("GET", "https://you.com/api/get_nonce", nil)
	req.Header.Set("Cookie", fmt.Sprintf("DS=%s", dsToken))

	resp, err := h.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取完整的响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 直接使用响应内容作为UUID
	return &NonceResponse{
		Uuid: strings.TrimSpace(string(body)),
	}, nil
}