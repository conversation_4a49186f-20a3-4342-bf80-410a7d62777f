package gptoss

// ChatCompletionRequest OpenAI聊天完成请求
type ChatCompletionRequest struct {
	Model           string             `json:"model"`
	Messages        []ChatMessage      `json:"messages"`
	Stream          bool               `json:"stream,omitempty"`
	ReasoningEffort string             `json:"reasoning_effort,omitempty"`
	Verbosity       string             `json:"verbosity,omitempty"`
	Temperature     float64            `json:"temperature,omitempty"`
	MaxTokens       int                `json:"max_tokens,omitempty"`
	TopP            float64            `json:"top_p,omitempty"`
}

// ChatMessage 聊天消息
type ChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatCompletionResponse OpenAI聊天完成响应
type ChatCompletionResponse struct {
	ID      string                 `json:"id"`
	Object  string                 `json:"object"`
	Created int64                  `json:"created"`
	Model   string                 `json:"model"`
	Choices []ChatCompletionChoice `json:"choices"`
	Usage   Usage                  `json:"usage"`
}

// ChatCompletionChoice 聊天完成选项
type ChatCompletionChoice struct {
	Index        int         `json:"index"`
	Message      ChatMessage `json:"message"`
	FinishReason string      `json:"finish_reason"`
}

// ChatCompletionChunk 流式响应块
type ChatCompletionChunk struct {
	ID      string                   `json:"id"`
	Object  string                   `json:"object"`
	Created int64                    `json:"created"`
	Model   string                   `json:"model"`
	Choices []ChatCompletionChunkChoice `json:"choices"`
}

// ChatCompletionChunkChoice 流式响应选项
type ChatCompletionChunkChoice struct {
	Index        int                    `json:"index"`
	Delta        ChatMessageDelta       `json:"delta"`
	FinishReason *string                `json:"finish_reason"`
}

// ChatMessageDelta 消息增量
type ChatMessageDelta struct {
	Content string `json:"content,omitempty"`
}

// Usage 使用情况统计
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// GptossRequest Gptoss API请求格式
type GptossRequest struct {
	ConversationID  *string `json:"conversation_id"`
	Model           string  `json:"model"`
	Content         string  `json:"content"`
	ReasoningEffort string  `json:"reasoning_effort"`
	Verbosity       string  `json:"verbosity,omitempty"`
}

// GptossResponse Gptoss API响应格式
type GptossResponse struct {
	Content string `json:"content"`
}

// GptossStreamResponse Gptoss流式响应格式
type GptossStreamResponse struct {
	Content string `json:"content"`
}

// ModelsResponse 模型列表响应
type ModelsResponse struct {
	Object string   `json:"object"`
	Data   []Model  `json:"data"`
}