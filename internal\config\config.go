package config

import (
	"log"
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

// Settings 应用配置
type Settings struct {
	Host                   string
	Port                   int
	UpstreamURL           string
	UpstreamModel         string
	ModelName             string
	ModelID               string
	APIKey                string
	ShowThinkTags         bool
	DefaultStream         bool
	MaxRequestsPerMinute  int
	LogLevel              string
	StreamTimeout         int
	Cookies               []string
	ProxyConfig           *ProxyConfig
	GMIConfig             *GMIConfig
}

// GMIConfig GMI相关配置
type GMIConfig struct {
	ValidToken    string
	TargetURL     string
	ModelsURL     string
	GMIBearerToken string
	UserAgents    []string
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	Enabled             bool
	Host                string
	Port                int
	Auth                string
	RejectUnauthorized  bool
}

// LoadConfig 加载配置
func LoadConfig() *Settings {
	// 加载.env文件
	loadEnvFile()

	settings := &Settings{
		Host:                  getEnv("HOST", "0.0.0.0"),
		Port:                  getEnvInt("PORT", 8000),
		UpstreamURL:          "https://chat.z.ai/api/chat/completions",
		UpstreamModel:        "0727-360B-API",
		ModelName:            "GLM-4.5",
		ModelID:              "GLM-4.5",
		APIKey:               getEnv("API_KEY", "sk-z2api-key-2024"),
		ShowThinkTags:        getEnvBool("SHOW_THINK_TAGS", false),
		DefaultStream:        getEnvBool("DEFAULT_STREAM", false),
		MaxRequestsPerMinute: getEnvInt("MAX_REQUESTS_PER_MINUTE", 60),
		LogLevel:             getEnv("LOG_LEVEL", "INFO"),
		StreamTimeout:        getEnvInt("STREAM_TIMEOUT", 30),
		ProxyConfig:          parseProxyConfig(),
		GMIConfig:            parseGMIConfig(),
	}

	settings.loadCookies()
	return settings
}

// loadEnvFile 加载.env文件
func loadEnvFile() {
	// 检查是否禁用.env文件加载
	if os.Getenv("DISABLE_DOTENV") == "true" || os.Getenv("DISABLE_DOTENV") == "1" {
		log.Println("ℹ️  .env file loading disabled by DISABLE_DOTENV environment variable")
		return
	}

	if err := godotenv.Load(); err != nil {
		log.Println("ℹ️  No .env file found, using system environment variables only")
	} else {
		log.Println("✅ Loaded environment variables from .env file")
	}
}

// parseProxyConfig 解析代理配置
func parseProxyConfig() *ProxyConfig {
	proxyConfig := &ProxyConfig{
		Enabled:            false,
		RejectUnauthorized: true,
	}

	// 支持多种代理环境变量格式
	proxyURL := getEnv("HTTP_PROXY", "")
	if proxyURL == "" {
		proxyURL = getEnv("http_proxy", "")
	}
	if proxyURL == "" {
		proxyURL = getEnv("HTTPS_PROXY", "")
	}
	if proxyURL == "" {
		proxyURL = getEnv("https_proxy", "")
	}

	if proxyURL != "" {
		// 这里简化处理，实际使用时可以用url.Parse解析
		log.Printf("✅ HTTP Proxy configured: %s", proxyURL)
		proxyConfig.Enabled = true
		// TODO: 解析代理URL获取host、port、auth等信息
	}

	return proxyConfig
}

// parseGMIConfig 解析GMI配置
func parseGMIConfig() *GMIConfig {
	userAgents := []string{
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
		"Mozilla/5.0 (Linux; Android 13; SM-S908B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
		"Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/114.0",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
	}

	return &GMIConfig{
		ValidToken:     getEnv("GMI_VALID_TOKEN", "gmi-free-2-api"),
		TargetURL:      getEnv("GMI_TARGET_URL", "https://console.gmicloud.ai/chat"),
		ModelsURL:      getEnv("GMI_MODELS_URL", "https://api.gmi-serving.com/v1/models"),
		GMIBearerToken: getEnv("GMI_BEARER_TOKEN", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImNhNGNkNGU1LTMyY2YtNDQ5OC1hNDZiLTFiYjFmMzI3NTUzMiIsInNjb3BlIjoiaWVfbW9kZWwiLCJjbGllbnRJZCI6IjAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCJ9.TTdQWMVpyx55Zb0oWqWcny1aYAl7yc_ctNmIphkkBfw"),
		UserAgents:     userAgents,
	}
}

// loadCookies 加载cookies
func (s *Settings) loadCookies() {
	cookiesStr := getEnv("Z_AI_COOKIES", "")
	if cookiesStr != "" && cookiesStr != "your_z_ai_cookie_here" {
		cookies := strings.Split(cookiesStr, ",")
		for _, cookie := range cookies {
			cookie = strings.TrimSpace(cookie)
			if len(cookie) > 0 {
				s.Cookies = append(s.Cookies, cookie)
			}
		}
	}

	if len(s.Cookies) == 0 {
		log.Println("⚠️  Warning: No valid Z.AI cookies configured!")
		log.Println("Please set Z_AI_COOKIES environment variable with comma-separated cookie values.")
		log.Println("Example: Z_AI_COOKIES=cookie1,cookie2,cookie3")
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取整数类型环境变量
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvBool 获取布尔类型环境变量
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		switch strings.ToLower(value) {
		case "true", "1", "yes":
			return true
		case "false", "0", "no":
			return false
		}
	}
	return defaultValue
}
