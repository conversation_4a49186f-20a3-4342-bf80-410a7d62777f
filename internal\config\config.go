package config

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Settings 应用配置
type Settings struct {
	Host                   string
	Port                   int
	UpstreamURL           string
	UpstreamModel         string
	ModelName             string
	ModelID               string
	APIKey                string
	ShowThinkTags         bool
	DefaultStream         bool
	MaxRequestsPerMinute  int
	LogLevel              string
	StreamTimeout         int
	Cookies               []string
	ProxyConfig           *ProxyConfig
	GMIConfig             *GMIConfig
	YouConfig             *YouConfig
	LogConfig             *LogConfig
	HTTPConfig            *HTTPConfig
	DeepinfraConfig       *DeepinfraConfig
}

// LogConfig 日志配置
type LogConfig struct {
	EnableFileLog    bool   // 是否启用文件日志
	LogFilePath      string // 日志文件路径
	MaxFileSize      int    // 最大文件大小(MB)
	MaxBackups       int    // 最大备份文件数
	MaxAge           int    // 最大保留天数
	Compress         bool   // 是否压缩旧日志文件
}

// GetEnableFileLog 实现LogConfigInterface接口
func (c *LogConfig) GetEnableFileLog() bool {
	return c.EnableFileLog
}

// GetLogFilePath 实现LogConfigInterface接口
func (c *LogConfig) GetLogFilePath() string {
	return c.LogFilePath
}

// GetMaxFileSize 实现LogConfigInterface接口
func (c *LogConfig) GetMaxFileSize() int {
	return c.MaxFileSize
}

// GetMaxBackups 实现LogConfigInterface接口
func (c *LogConfig) GetMaxBackups() int {
	return c.MaxBackups
}

// GetMaxAge 实现LogConfigInterface接口
func (c *LogConfig) GetMaxAge() int {
	return c.MaxAge
}

// GetCompress 实现LogConfigInterface接口
func (c *LogConfig) GetCompress() bool {
	return c.Compress
}

// YouConfig You.com相关配置
type YouConfig struct {
	DSToken         string   // You.com DS token
	AgentModelIDs   []string // Agent模型ID列表
	DefaultModel    string   // 默认模型
	MaxContextToken int      // 最大上下文token数
	UsePostMethod   bool     // 是否使用POST方式请求，默认true
}

// GMIConfig GMI相关配置
type GMIConfig struct {
	ValidToken    string
	TargetURL     string
	ModelsURL     string
	GMIBearerToken string
	UserAgents    []string
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	Enabled             bool
	Host                string
	Port                int
	Auth                string
	RejectUnauthorized  bool
}

// HTTPConfig HTTP客户端全局配置
type HTTPConfig struct {
	InsecureSkipVerify bool // 是否跳过SSL证书验证
}

// LoadConfig 加载配置
func LoadConfig() *Settings {
	// 加载.env文件
	loadEnvFile()

	settings := &Settings{
		Host:                  getEnv("HOST", "0.0.0.0"),
		Port:                  getEnvInt("PORT", 8000),
		UpstreamURL:          "https://chat.z.ai/api/chat/completions",
		UpstreamModel:        "0727-360B-API",
		ModelName:            "GLM-4.5",
		ModelID:              "GLM-4.5",
		APIKey:               getEnv("API_KEY", "sk-z2api-key-2024"),
		ShowThinkTags:        getEnvBool("SHOW_THINK_TAGS", false),
		DefaultStream:        getEnvBool("DEFAULT_STREAM", false),
		MaxRequestsPerMinute: getEnvInt("MAX_REQUESTS_PER_MINUTE", 60),
		LogLevel:             getEnv("LOG_LEVEL", "INFO"),
		StreamTimeout:        getEnvInt("STREAM_TIMEOUT", 30),
		ProxyConfig:          parseProxyConfig(),
		GMIConfig:            parseGMIConfig(),
		YouConfig:            parseYouConfig(),
		LogConfig:            parseLogConfig(),
		HTTPConfig:           parseHTTPConfig(),
		DeepinfraConfig:      parseDeepinfraConfig(),
	}

	settings.loadCookies()
	return settings
}

// loadEnvFile 加载.env文件
func loadEnvFile() {
	// 检查是否禁用.env文件加载
	if os.Getenv("DISABLE_DOTENV") == "true" || os.Getenv("DISABLE_DOTENV") == "1" {
		log.Println("ℹ️  .env file loading disabled by DISABLE_DOTENV environment variable")
		return
	}

	if err := godotenv.Load(); err != nil {
		log.Println("ℹ️  No .env file found, using system environment variables only")
	} else {
		log.Println("✅ Loaded environment variables from .env file")
	}
}

// parseProxyConfig 解析代理配置
func parseProxyConfig() *ProxyConfig {
	proxyConfig := &ProxyConfig{
		Enabled:            false,
		RejectUnauthorized: true,
	}

	// 支持多种代理环境变量格式
	proxyURL := getEnv("HTTP_PROXY", "")
	if proxyURL == "" {
		proxyURL = getEnv("http_proxy", "")
	}
	if proxyURL == "" {
		proxyURL = getEnv("HTTPS_PROXY", "")
	}
	if proxyURL == "" {
		proxyURL = getEnv("https_proxy", "")
	}

	if proxyURL != "" {
		// 解析代理URL获取host、port、auth等信息
		if parsedURL, err := url.Parse(proxyURL); err == nil {
			proxyConfig.Enabled = true
			proxyConfig.Host = parsedURL.Hostname()

			// 获取端口，如果没有指定则使用默认端口
			port := parsedURL.Port()
			if port != "" {
				if portInt, err := strconv.Atoi(port); err == nil {
					proxyConfig.Port = portInt
				}
			} else {
				// 根据协议设置默认端口
				if parsedURL.Scheme == "https" {
					proxyConfig.Port = 443
				} else {
					proxyConfig.Port = 8080
				}
			}

			// 获取认证信息
			if parsedURL.User != nil {
				if password, hasPassword := parsedURL.User.Password(); hasPassword {
					proxyConfig.Auth = parsedURL.User.Username() + ":" + password
				} else {
					proxyConfig.Auth = parsedURL.User.Username()
				}
			}

			log.Printf("✅ HTTP Proxy configured: %s:%d", proxyConfig.Host, proxyConfig.Port)
			if proxyConfig.Auth != "" {
				log.Printf("✅ Proxy authentication configured")
			}
		} else {
			log.Printf("❌ Failed to parse proxy URL: %s, error: %v", proxyURL, err)
		}
	}

	return proxyConfig
}

// parseYouConfig 解析You.com配置
func parseYouConfig() *YouConfig {
	// 解析Agent模型ID
	var agentModelIDs []string
	agentModelIDsStr := getEnv("YOU_AGENT_MODEL_IDS", "")
	if agentModelIDsStr != "" {
		ids := strings.Split(agentModelIDsStr, ",")
		for _, id := range ids {
			id = strings.TrimSpace(id)
			if id != "" {
				agentModelIDs = append(agentModelIDs, id)
			}
		}
	}

	return &YouConfig{
		DSToken:         getEnv("YOU_DS_TOKEN", ""),
		AgentModelIDs:   agentModelIDs,
		DefaultModel:    getEnv("YOU_DEFAULT_MODEL", "deepseek-chat"),
		MaxContextToken: getEnvInt("YOU_MAX_CONTEXT_TOKEN", 30),
		UsePostMethod:   getEnvBool("YOU_USE_POST_METHOD", true),
	}
}

// parseGMIConfig 解析GMI配置
func parseGMIConfig() *GMIConfig {
	userAgents := []string{
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
		"Mozilla/5.0 (Linux; Android 13; SM-S908B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
		"Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/114.0",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
	}

	return &GMIConfig{
		ValidToken:     getEnv("GMI_VALID_TOKEN", "gmi-free-2-api"),
		TargetURL:      getEnv("GMI_TARGET_URL", "https://console.gmicloud.ai/chat"),
		ModelsURL:      getEnv("GMI_MODELS_URL", "https://api.gmi-serving.com/v1/models"),
		GMIBearerToken: getEnv("GMI_BEARER_TOKEN", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImNhNGNkNGU1LTMyY2YtNDQ5OC1hNDZiLTFiYjFmMzI3NTUzMiIsInNjb3BlIjoiaWVfbW9kZWwiLCJjbGllbnRJZCI6IjAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCJ9.TTdQWMVpyx55Zb0oWqWcny1aYAl7yc_ctNmIphkkBfw"),
		UserAgents:     userAgents,
	}
}

// parseLogConfig 解析日志配置
func parseLogConfig() *LogConfig {
	return &LogConfig{
		EnableFileLog: getEnvBool("LOG_ENABLE_FILE", false),
		LogFilePath:   getEnv("LOG_FILE_PATH", "logs/oh-my-ai2api.log"),
		MaxFileSize:   getEnvInt("LOG_MAX_FILE_SIZE", 100), // 默认100MB
		MaxBackups:    getEnvInt("LOG_MAX_BACKUPS", 3),     // 默认保留3个备份文件
		MaxAge:        getEnvInt("LOG_MAX_AGE", 28),        // 默认保留28天
		Compress:      getEnvBool("LOG_COMPRESS", true),    // 默认压缩旧日志
	}
}

// loadCookies 加载cookies
func (s *Settings) loadCookies() {
	cookiesStr := getEnv("Z_AI_COOKIES", "")
	if cookiesStr != "" && cookiesStr != "your_z_ai_cookie_here" {
		cookies := strings.Split(cookiesStr, ",")
		for _, cookie := range cookies {
			cookie = strings.TrimSpace(cookie)
			if len(cookie) > 0 {
				s.Cookies = append(s.Cookies, cookie)
			}
		}
	}

	if len(s.Cookies) == 0 {
		log.Println("⚠️  Warning: No valid Z.AI cookies configured!")
		log.Println("Please set Z_AI_COOKIES environment variable with comma-separated cookie values.")
		log.Println("Example: Z_AI_COOKIES=cookie1,cookie2,cookie3")
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取整数类型环境变量
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// parseHTTPConfig 解析HTTP配置
func parseHTTPConfig() *HTTPConfig {
	return &HTTPConfig{
		InsecureSkipVerify: getEnvBool("HTTP_INSECURE_SKIP_VERIFY", false),
	}
}

// getEnvBool 获取布尔类型环境变量
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		switch strings.ToLower(value) {
		case "true", "1", "yes":
			return true
		case "false", "0", "no":
			return false
		}
	}
	return defaultValue
}

// DeepinfraConfig Deepinfra配置
type DeepinfraConfig struct {
	Enabled   bool
	Token     string
	TargetURL string
	Models    []DeepinfraModel
}

// DeepinfraModel Deepinfra模型信息
type DeepinfraModel struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	OwnedBy string `json:"owned_by"`
}

// parseDeepinfraConfig 解析Deepinfra配置
func parseDeepinfraConfig() *DeepinfraConfig {
	return &DeepinfraConfig{
		Enabled:   getEnvBool("DEEPINFRA_ENABLED", false),
		Token:     getEnv("DEEPINFRA_TOKEN", ""),
		TargetURL: getEnv("DEEPINFRA_TARGET_URL", "https://api.deepinfra.com/v1/openai/chat/completions"),
		Models:    getDefaultDeepinfraModels(),
	}
}

// DeepinfraAPIResponse Deepinfra API响应结构
// DeepinfraAPIModel Deepinfra API模型结构
type DeepinfraAPIModel struct {
	ModelName     string                 `json:"model_name"`
	Type          string                 `json:"type"`
	ReportedType  string                 `json:"reported_type"`
	Description   string                 `json:"description"`
	CoverImgURL   string                 `json:"cover_img_url"`
	Tags          []string               `json:"tags"`
	Pricing       DeepinfraPricing       `json:"pricing"`
	MaxTokens     interface{}            `json:"max_tokens"`
	ReplacedBy    interface{}            `json:"replaced_by"`
	Deprecated    interface{}            `json:"deprecated"`
	Quantization  interface{}            `json:"quantization"`
	MMLU          interface{}            `json:"mmlu"`
	Expected      interface{}            `json:"expected"`
	Private       int                    `json:"private"`
}

// DeepinfraPricing 定价信息
type DeepinfraPricing struct {
	Type                 string  `json:"type"`
	CentsPerInputToken   float64 `json:"cents_per_input_token"`
	CentsPerOutputToken  float64 `json:"cents_per_output_token"`
}

// fetchDeepinfraModels 从API获取Deepinfra模型列表
func fetchDeepinfraModels() ([]DeepinfraModel, error) {
	// 使用全局HTTP配置创建客户端
	httpConfig := parseHTTPConfig()
	proxyConfig := parseProxyConfig()

	// 创建HTTP客户端，使用全局配置
	client := createHTTPClientForConfig(httpConfig, proxyConfig, 10*time.Second)

	// 发送请求到新的API端点
	resp, err := client.Get("https://api.deepinfra.com/models/featured")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status: %d", resp.StatusCode)
	}

	// 解析JSON响应 - 直接解析为模型数组
	var apiModels []DeepinfraAPIModel
	if err := json.NewDecoder(resp.Body).Decode(&apiModels); err != nil {
		return nil, err
	}

	// 转换为DeepinfraModel格式
	var models []DeepinfraModel
	currentTime := time.Now().Unix()

	for _, apiModel := range apiModels {
		// 只包含公开的文本生成模型
		if apiModel.Private == 0 && apiModel.Type == "text-generation" {
			// 从model_name中提取owner信息
			parts := strings.Split(apiModel.ModelName, "/")
			owner := "deepinfra"
			if len(parts) > 1 {
				owner = parts[0]
			}

			model := DeepinfraModel{
				ID:      apiModel.ModelName,
				Object:  "model",
				Created: currentTime,
				OwnedBy: owner,
			}
			models = append(models, model)
		}
	}

	return models, nil
}

// getDefaultDeepinfraModels 获取Deepinfra模型列表（优先从API获取，失败时使用静态列表）
func getDefaultDeepinfraModels() []DeepinfraModel {
	// 尝试从API获取最新模型列表
	if models, err := fetchDeepinfraModels(); err == nil && len(models) > 0 {
		log.Printf("✅ Deepinfra models fetched from API: %d models", len(models))
		return models
	} else {
		if err != nil {
			log.Printf("⚠️  Failed to fetch Deepinfra models from API: %v", err)
		}
		log.Printf("🔄 Using static Deepinfra model list as fallback")
	}

	// 回退到静态模型列表
	return []DeepinfraModel{
		{
			ID:      "deepseek-ai/DeepSeek-R1-0528-Turbo",
			Object:  "model",
			Created: time.Date(2024, 6, 1, 0, 0, 0, 0, time.UTC).Unix(),
			OwnedBy: "deepseek-ai",
		},
		{
			ID:      "deepseek-ai/DeepSeek-V3-0324-Turbo",
			Object:  "model",
			Created: time.Date(2024, 3, 24, 0, 0, 0, 0, time.UTC).Unix(),
			OwnedBy: "deepseek-ai",
		},
		{
			ID:      "deepseek-ai/DeepSeek-R1-Distill-Llama-70B",
			Object:  "model",
			Created: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC).Unix(),
			OwnedBy: "deepseek-ai",
		},
	}
}

// createHTTPClientForConfig 为配置创建HTTP客户端（简化版，用于内部使用）
func createHTTPClientForConfig(httpConfig *HTTPConfig, proxyConfig *ProxyConfig, timeout time.Duration) *http.Client {
	// 创建传输配置
	transport := &http.Transport{}

	// 配置SSL
	if httpConfig != nil && httpConfig.InsecureSkipVerify {
		transport.TLSClientConfig = &tls.Config{
			InsecureSkipVerify: true,
		}
		log.Printf("HTTP client configured with SSL certificate verification disabled")
	}

	// 配置代理
	if proxyConfig != nil && proxyConfig.Enabled && proxyConfig.Host != "" {
		var proxyURL string
		if proxyConfig.Auth != "" {
			proxyURL = fmt.Sprintf("http://%s@%s:%d", proxyConfig.Auth, proxyConfig.Host, proxyConfig.Port)
		} else {
			proxyURL = fmt.Sprintf("http://%s:%d", proxyConfig.Host, proxyConfig.Port)
		}

		if parsedURL, err := url.Parse(proxyURL); err == nil {
			transport.Proxy = http.ProxyURL(parsedURL)
			log.Printf("✅ HTTP Proxy configured: %s:%d", proxyConfig.Host, proxyConfig.Port)
		} else {
			log.Printf("⚠️  Failed to parse proxy URL: %v", err)
		}
	}

	return &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}
}
