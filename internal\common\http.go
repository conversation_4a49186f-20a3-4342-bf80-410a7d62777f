package common

import (
	"crypto/tls"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"oh-my-ai2api/internal/config"
)

// CreateHTTPClient 创建HTTP客户端，支持全局SSL和代理配置
func CreateHTTPClient(httpConfig *config.HTTPConfig, proxyConfig *config.ProxyConfig, timeout time.Duration, logger *Logger) *http.Client {
	// 创建传输配置
	transport := &http.Transport{}

	// 配置SSL
	if httpConfig != nil && httpConfig.InsecureSkipVerify {
		transport.TLSClientConfig = &tls.Config{
			InsecureSkipVerify: true,
		}
		if logger != nil {
			logger.Info("HTTP client configured with SSL certificate verification disabled")
		}
	}

	// 配置代理
	if proxyConfig != nil && proxyConfig.Enabled && proxyConfig.Host != "" {
		var proxyURL string
		if proxyConfig.Auth != "" {
			// 包含认证信息的代理URL
			proxyURL = fmt.Sprintf("http://%s@%s:%d", proxyConfig.Auth, proxyConfig.Host, proxyConfig.Port)
		} else {
			// 不包含认证信息的代理URL
			proxyURL = fmt.Sprintf("http://%s:%d", proxyConfig.Host, proxyConfig.Port)
		}

		if parsedURL, err := url.Parse(proxyURL); err == nil {
			transport.Proxy = http.ProxyURL(parsedURL)
			if logger != nil {
				// 日志中不显示认证信息，保护敏感数据
				logURL := fmt.Sprintf("http://%s:%d", proxyConfig.Host, proxyConfig.Port)
				logger.Info(fmt.Sprintf("HTTP client configured with proxy: %s", logURL))
				if proxyConfig.Auth != "" {
					logger.Info("Proxy authentication enabled")
				}
			}
		}
	}

	// 创建客户端
	client := &http.Client{
		Timeout:   timeout,
		Transport: transport,
	}

	return client
}

// CreateHTTPClientWithDefaults 创建带默认配置的HTTP客户端
func CreateHTTPClientWithDefaults(settings *config.Settings, logger *Logger) *http.Client {
	return CreateHTTPClient(settings.HTTPConfig, settings.ProxyConfig, 60*time.Second, logger)
}
