package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
	"oh-my-ai2api/internal/server"
)

// main 主函数
func main() {
	// 加载配置
	settings := config.LoadConfig()

	// 创建日志器
	logger := common.NewLogger(settings.LogLevel)

	// 创建服务器
	srv := server.NewServer(settings, logger)

	// 设置信号处理
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	// 启动服务器
	go func() {
		if err := srv.Start(); err != nil {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待退出信号
	<-c
	logger.Info("Shutting down server...")
}