package gptoss

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
)

// Proxy Gptoss代理
type Proxy struct {
	config     *GptossConfig
	httpClient *http.Client
	logger     *common.Logger
}

// NewProxy 创建新的Gptoss代理
func NewProxy(config *GptossConfig, httpConfig *config.HTTPConfig, proxyConfig *config.ProxyConfig, logger *common.Logger) *Proxy {
	// 使用通用HTTP客户端创建函数
	httpClient := common.CreateHTTPClient(httpConfig, proxyConfig, 30*time.Second, logger)

	return &Proxy{
		config:     config,
		httpClient: httpClient,
		logger:     logger,
	}
}

// SendRequest 发送请求到Gptoss API
func (p *Proxy) SendRequest(request *GptossRequest) (*GptossResponse, error) {
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", p.config.TargetURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	p.setHeaders(req)

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response GptossResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response, nil
}

// SendStreamRequest 发送流式请求到Gptoss API
func (p *Proxy) SendStreamRequest(request *GptossRequest) (*http.Response, error) {
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", p.config.TargetURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	p.setHeaders(req)
	req.Header.Set("Accept", "text/event-stream")

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		resp.Body.Close()
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	return resp, nil
}

// ParseSSEStream 解析SSE流
func (p *Proxy) ParseSSEStream(resp *http.Response, model string) <-chan string {
	ch := make(chan string)

	go func() {
		defer close(ch)
		
		scanner := bufio.NewScanner(resp.Body)
		contentBuffer := ""
		messageID := common.GenerateUUID()
		
		for scanner.Scan() {
			line := scanner.Text()
			
			if line == "" {
				continue
			}

			if strings.HasPrefix(line, "event:message") {
				continue
			} else if strings.HasPrefix(line, "data:") {
				dataContent := strings.TrimPrefix(line, "data:")
				var streamResp GptossStreamResponse
				
				if err := json.Unmarshal([]byte(dataContent), &streamResp); err == nil {
					if streamResp.Content != "" {
						contentBuffer += streamResp.Content
						
						chunk := ChatCompletionChunk{
							ID:      fmt.Sprintf("chatcmpl-%s", messageID),
							Object:  "chat.completion.chunk",
							Created: 1677652288,
							Model:   model,
							Choices: []ChatCompletionChunkChoice{
								{
									Index: 0,
									Delta: ChatMessageDelta{
										Content: streamResp.Content,
									},
									FinishReason: nil,
								},
							},
						}
						
						chunkJSON, _ := json.Marshal(chunk)
						ch <- fmt.Sprintf("data: %s\n\n", string(chunkJSON))
					}
				}
			} else if strings.HasPrefix(line, "event:summary") {
				// 发送结束块
				finalChunk := ChatCompletionChunk{
					ID:      fmt.Sprintf("chatcmpl-%s", messageID),
					Object:  "chat.completion.chunk",
					Created: 1677652288,
					Model:   model,
					Choices: []ChatCompletionChunkChoice{
						{
							Index: 0,
							Delta: ChatMessageDelta{},
							FinishReason: func() *string {
								finishReason := "stop"
								return &finishReason
							}(),
						},
					},
				}
				
				finalChunkJSON, _ := json.Marshal(finalChunk)
				ch <- fmt.Sprintf("data: %s\n\n", string(finalChunkJSON))
				ch <- "data: [DONE]\n\n"
				break
			}
		}
		
		resp.Body.Close()
	}()

	return ch
}

// setHeaders 设置请求头
func (p *Proxy) setHeaders(req *http.Request) {
	req.Header.Set("Accept-Language", "zh-TW,zh;q=0.8")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Cookie", fmt.Sprintf("guest_session_id=%s; _pk_id.48.98b5=%s; _pk_ses.48.98b5=1", p.config.SessionID, p.config.PkID))
	req.Header.Set("Origin", "https://chat-gpt-oss.com")
	req.Header.Set("Referer", "https://chat-gpt-oss.com/")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36")
	req.Header.Set("X-Fingerprint", p.config.Fingerprint)
}