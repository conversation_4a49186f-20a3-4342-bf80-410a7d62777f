name: Release

on:
  push:
    tags:
      - 'v*'  # 当推送以 v 开头的标签时触发，如 v1.0.0
  workflow_dispatch:  # 允许手动触发

jobs:
  build-windows:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'  # 根据你的项目需要调整Go版本
        
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~\AppData\Local\go-build
          ~\go\pkg\mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
          
    - name: Download dependencies
      run: go mod download
      
    - name: Build for Windows AMD64
      run: |
        $env:GOOS="windows"
        $env:GOARCH="amd64"
        go build -ldflags "-s -w" -o oh-my-ai2api-windows-amd64.exe .
        
    - name: Build for Windows ARM64
      run: |
        $env:GOOS="windows"
        $env:GOARCH="arm64"
        go build -ldflags "-s -w" -o oh-my-ai2api-windows-arm64.exe .
        
    - name: Create release archive
      run: |
        # 创建发布目录
        New-Item -ItemType Directory -Force -Path release
        
        # 复制可执行文件
        Copy-Item oh-my-ai2api-windows-amd64.exe release/
        Copy-Item oh-my-ai2api-windows-arm64.exe release/
        
        # 复制配置文件示例
        Copy-Item .env.example release/
        Copy-Item README.md release/
        
        # 创建压缩包
        Compress-Archive -Path release/* -DestinationPath oh-my-ai2api-windows.zip
        
    - name: Get version from tag
      id: get_version
      run: |
        if ($env:GITHUB_REF -match "refs/tags/(.*)") {
          $version = $matches[1]
        } else {
          $version = "dev-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        }
        echo "VERSION=$version" >> $env:GITHUB_OUTPUT
        echo "Version: $version"
        
    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ steps.get_version.outputs.VERSION }}
        release_name: Release ${{ steps.get_version.outputs.VERSION }}
        body: |
          ## Oh My AI2API - Multi AI Proxy ${{ steps.get_version.outputs.VERSION }}
          
          ### 功能特性
          - 🚀 OpenAI兼容的API接口
          - 🔄 支持流式和非流式响应
          - 🤖 多AI服务支持：Z.AI 和 GMI Cloud
          - 🔐 灵活的认证机制
          - 🌐 代理支持
          - 📊 健康检查和自动恢复
          - 📝 详细的请求/响应日志记录
          
          ### 下载说明
          - `oh-my-ai2api-windows-amd64.exe`: 适用于 Windows x64 系统
          - `oh-my-ai2api-windows-arm64.exe`: 适用于 Windows ARM64 系统
          - `oh-my-ai2api-windows.zip`: 包含所有文件的完整压缩包
          
          ### 使用方法
          1. 下载对应架构的可执行文件
          2. 复制 `.env.example` 为 `.env` 并配置相关参数
          3. 运行可执行文件
          
          详细使用说明请参考 [README.md](https://github.com/${{ github.repository }}/blob/main/README.md)
        draft: false
        prerelease: false
        
    - name: Upload Windows AMD64 Binary
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./oh-my-ai2api-windows-amd64.exe
        asset_name: oh-my-ai2api-windows-amd64.exe
        asset_content_type: application/octet-stream
        
    - name: Upload Windows ARM64 Binary
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./oh-my-ai2api-windows-arm64.exe
        asset_name: oh-my-ai2api-windows-arm64.exe
        asset_content_type: application/octet-stream
        
    - name: Upload Release Archive
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./oh-my-ai2api-windows.zip
        asset_name: oh-my-ai2api-windows.zip
        asset_content_type: application/zip
