name: Release

on:
  push:
    tags:
      - 'v*'  # 当推送以 v 开头的标签时触发，如 v1.0.0
  workflow_dispatch:  # 允许手动触发

permissions:
  contents: write  # 允许创建 release 和上传文件

jobs:
  build-windows:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'  # 根据你的项目需要调整Go版本
        
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~\AppData\Local\go-build
          ~\go\pkg\mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
          
    - name: Download dependencies
      run: go mod download
      
    - name: Build for Windows AMD64
      run: |
        $env:GOOS="windows"
        $env:GOARCH="amd64"
        go build -ldflags "-s -w" -o oh-my-ai2api-windows-amd64.exe .
        
    # - name: Build for Windows ARM64
    #   run: |
    #     $env:GOOS="windows"
    #     $env:GOARCH="arm64"
    #     go build -ldflags "-s -w" -o oh-my-ai2api-windows-arm64.exe .
        

        
    - name: Get version from tag
      id: get_version
      run: |
        if ($env:GITHUB_REF -match "refs/tags/(.*)") {
          $version = $matches[1]
        } else {
          $version = "dev-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        }
        echo "VERSION=$version" >> $env:GITHUB_OUTPUT
        echo "Version: $version"
        
    - name: Create Release and Upload Assets
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ steps.get_version.outputs.VERSION }}
        name: Release ${{ steps.get_version.outputs.VERSION }}
        body: |
          ## Oh My AI2API - Multi AI Proxy ${{ steps.get_version.outputs.VERSION }}

          ### 功能特性
          - 🚀 OpenAI兼容的API接口
          - 🔄 支持流式和非流式响应
          - 🤖 多AI服务支持：Z.AI 和 GMI Cloud
          - 🔐 灵活的认证机制
          - 🌐 代理支持
          - 📊 健康检查和自动恢复
          - 📝 详细的请求/响应日志记录

          ### 下载说明
          - `oh-my-ai2api-windows-amd64.exe`: 适用于 Windows x64 系统

          ### 使用方法
          1. 下载 `oh-my-ai2api-windows-amd64.exe` 可执行文件
          2. 从仓库下载 `.env.example` 文件，重命名为 `.env` 并配置相关参数
          3. 运行可执行文件

          详细使用说明请参考 [README.md](https://github.com/${{ github.repository }}/blob/main/README.md)
        draft: false
        prerelease: false
        files: |
          oh-my-ai2api-windows-amd64.exe
