package deepinfra

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"time"

	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
)

// Proxy Deepinfra代理
type Proxy struct {
	config     *config.DeepinfraConfig
	httpClient *http.Client
	logger     *common.Logger
}

// NewProxy 创建新的Deepinfra代理
func NewProxy(deepinfraConfig *config.DeepinfraConfig, httpConfig *config.HTTPConfig, proxyConfig *config.ProxyConfig, logger *common.Logger) *Proxy {
	// 使用通用HTTP客户端创建函数
	httpClient := common.CreateHTTPClient(httpConfig, proxyConfig, 60*time.Second, logger)

	return &Proxy{
		config:     deepinfraConfig,
		httpClient: httpClient,
		logger:     logger,
	}
}

// ProxyRequest 代理请求到Deepinfra
func (p *Proxy) ProxyRequest(requestBody []byte) (*http.Response, error) {
	p.logger.Debug("=== Deepinfra Proxy Request Started ===")

	// 创建请求
	req, err := http.NewRequest("POST", p.config.TargetURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头（模拟浏览器）
	p.setRequestHeaders(req)

	p.logger.Debug(fmt.Sprintf("Proxying request to: %s", p.config.TargetURL))

	// 发送请求
	resp, err := p.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	p.logger.Debug(fmt.Sprintf("Received response with status: %d", resp.StatusCode))
	return resp, nil
}

// setRequestHeaders 设置请求头
func (p *Proxy) setRequestHeaders(req *http.Request) {
	// 设置浏览器模拟头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
	req.Header.Set("Accept", "text/event-stream")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("sec-ch-ua-platform", "\"Windows\"")
	req.Header.Set("X-Deepinfra-Source", "web-page")
	req.Header.Set("sec-ch-ua", "\"Not(A:Brand\";v=\"99\", \"Microsoft Edge\";v=\"133\", \"Chromium\";v=\"133\"")
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("Origin", "https://deepinfra.com")
	req.Header.Set("Sec-Fetch-Site", "same-site")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Referer", "https://deepinfra.com/")
}

// ValidateRequest 验证请求
func (p *Proxy) ValidateRequest(request *ChatCompletionRequest) error {
	if len(request.Messages) == 0 {
		return fmt.Errorf("no messages provided")
	}

	if request.Model == "" {
		return fmt.Errorf("no model specified")
	}

	return nil
}

// CopyResponseHeaders 复制响应头
func (p *Proxy) CopyResponseHeaders(src http.Header, dst http.Header) {
	// 复制Content-Type
	if contentType := src.Get("Content-Type"); contentType != "" {
		dst.Set("Content-Type", contentType)
	}

	// 复制其他相关头
	headersToKeep := []string{
		"Content-Encoding",
		"Transfer-Encoding",
		"Cache-Control",
		"X-Request-ID",
	}

	for _, header := range headersToKeep {
		if value := src.Get(header); value != "" {
			dst.Set(header, value)
		}
	}

	// 设置CORS头
	dst.Set("Access-Control-Allow-Origin", "*")
	dst.Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS")
	dst.Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
}

// CopyResponseBody 复制响应体
func (p *Proxy) CopyResponseBody(src io.Reader, dst io.Writer) error {
	_, err := io.Copy(dst, src)
	return err
}
