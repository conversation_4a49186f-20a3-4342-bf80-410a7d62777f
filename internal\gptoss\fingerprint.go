package gptoss

import (
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"runtime"
	"sort"
	"strings"
	"time"
)

// FingerprintComponent 指纹组件
type FingerprintComponent struct {
	Value interface{} `json:"value"`
	Error string      `json:"error,omitempty"`
}

// FingerprintData 指纹数据
type FingerprintData struct {
	VisitorID  string                        `json:"visitorId"`
	Components map[string]FingerprintComponent `json:"components"`
	Confidence float64                       `json:"confidence"`
}

// FingerprintGenerator 指纹生成器
type FingerprintGenerator struct {
	components map[string]FingerprintComponent
}

// NewFingerprintGenerator 创建新的指纹生成器
func NewFingerprintGenerator() *FingerprintGenerator {
	return &FingerprintGenerator{
		components: make(map[string]FingerprintComponent),
	}
}

// getScreenResolution 获取屏幕分辨率
func (fg *FingerprintGenerator) getScreenResolution() []int {
	// 默认值，实际应用中可能需要调用系统API
	return []int{1920, 1080}
}

// getTimezone 获取时区偏移
func (fg *FingerprintGenerator) getTimezone() int {
	_, offset := time.Now().Zone()
	return offset / 60
}

// getLanguages 获取系统语言
func (fg *FingerprintGenerator) getLanguages() []string {
	return []string{"en-US"}
}

// getPlatformInfo 获取平台信息
func (fg *FingerprintGenerator) getPlatformInfo() string {
	return runtime.GOOS + " " + runtime.GOARCH
}

// getHardwareConcurrency 获取CPU核心数
func (fg *FingerprintGenerator) getHardwareConcurrency() int {
	return runtime.NumCPU()
}

// getColorDepth 获取颜色深度
func (fg *FingerprintGenerator) getColorDepth() int {
	return 24
}

// getDeviceMemory 获取设备内存（近似值）
func (fg *FingerprintGenerator) getDeviceMemory() int {
	// 默认8GB
	return 8
}

// getCanvasFingerprint 生成canvas指纹
func (fg *FingerprintGenerator) getCanvasFingerprint() string {
	randomComponent := rand.Intn(9000) + 1000
	canvasData := fmt.Sprintf("canvas_%s_%d_%d", runtime.GOOS, fg.getHardwareConcurrency(), randomComponent)
	
	hash := sha256.Sum256([]byte(canvasData))
	return fmt.Sprintf("%x", hash)[:16]
}

// getWebGLInfo 获取WebGL信息
func (fg *FingerprintGenerator) getWebGLInfo() map[string]interface{} {
	return map[string]interface{}{
		"vendor":                   "Google Inc. (Intel)",
		"renderer":                "ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)",
		"version":                 "WebGL 1.0 (OpenGL ES 2.0 Chromium)",
		"shadingLanguageVersion":  "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)",
	}
}

// getAudioFingerprint 获取音频指纹
func (fg *FingerprintGenerator) getAudioFingerprint() int {
	return 44100
}

// getFonts 获取可用字体
func (fg *FingerprintGenerator) getFonts() []string {
	return []string{"Arial", "Helvetica", "Times New Roman", "Courier New"}
}

// getPlugins 获取浏览器插件
func (fg *FingerprintGenerator) getPlugins() []interface{} {
	return []interface{}{}
}

// getTouchSupport 获取触摸支持
func (fg *FingerprintGenerator) getTouchSupport() []interface{} {
	return []interface{}{0, false, false}
}

// getVendorInfo 获取供应商信息
func (fg *FingerprintGenerator) getVendorInfo() string {
	return "Google Inc."
}

// getCookiesEnabled 检查cookie是否启用
func (fg *FingerprintGenerator) getCookiesEnabled() bool {
	return true
}

// getStorageInfo 获取存储信息
func (fg *FingerprintGenerator) getStorageInfo() map[string]interface{} {
	return map[string]interface{}{
		"sessionStorage": true,
		"localStorage":  true,
		"indexedDB":     true,
		"openDatabase":  false,
	}
}

// getMathValues 获取数学计算值
func (fg *FingerprintGenerator) getMathValues() map[string]interface{} {
	return map[string]interface{}{
		"acos":       math.Acos(1.0),
		"acosh":      math.Acosh(1.0),
		"acoshPf":    math.Acosh(1.0) / 2.0,
		"asin":       math.Asin(0.1234),
		"asinh":      math.Asinh(0.5),
		"asinhPf":    math.Asinh(0.5),
		"atanh":      math.Atanh(0.5),
		"atanhPf":    math.Atanh(0.5),
		"atan":       math.Atan(0.5),
		"sin":        math.Sin(1.0),
		"sinh":       math.Sinh(1.0),
		"sinhPf":     math.Sinh(1.0),
		"cos":        math.Cos(1.0),
		"cosh":       math.Cosh(1.0),
		"coshPf":     math.Cosh(1.0),
		"tan":        math.Tan(1.0),
		"tanh":       math.Tanh(1.0),
		"tanhPf":     math.Tanh(1.0),
		"exp":        math.Exp(1.0),
		"expm1":      math.Expm1(1.0),
		"expm1Pf":    math.Expm1(1.0),
		"log1p":      math.Log1p(1.0),
		"log1pPf":    math.Log1p(1.0),
		"powPI":      math.Pow(math.Pi, math.Pi),
	}
}

// getWebGLExtensions 获取WebGL扩展信息
func (fg *FingerprintGenerator) getWebGLExtensions() map[string]interface{} {
	return map[string]interface{}{
		"contextAttributes":      []string{"alpha=true", "antialias=true", "depth=true", "premultipliedAlpha=true", "preserveDrawingBuffer=false", "stencil=false"},
		"parameters":            []string{"MAX_VERTEX_ATTRIBS=16", "MAX_VERTEX_UNIFORM_VECTORS=254", "MAX_FRAGMENT_UNIFORM_VECTORS=221"},
		"shaderPrecisions":      []string{"VERTEX_SHADER.HIGH_FLOAT=23,127,23", "FRAGMENT_SHADER.HIGH_FLOAT=23,127,23"},
		"extensions":            []string{"ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float"},
		"extensionParameters":   []string{},
		"unsupportedExtensions": []string{},
	}
}

// collectComponents 收集所有指纹组件
func (fg *FingerprintGenerator) collectComponents() map[string]FingerprintComponent {
	fg.components = map[string]FingerprintComponent{
		"fonts":               {Value: fg.getFonts()},
		"domBlockers":         {Value: []string{}},
		"fontPreferences":     {Value: map[string]interface{}{"default": 120, "apple": 120, "serif": 120, "sans": 120, "mono": 120, "min": 120, "system": 120}},
		"audio":               {Value: fg.getAudioFingerprint()},
		"screenFrame":         {Value: []int{0, 0, 0, 0}},
		"canvas":              {Value: fg.getCanvasFingerprint()},
		"osCpu":               {Value: runtime.GOARCH},
		"languages":           {Value: fg.getLanguages()},
		"colorDepth":          {Value: fg.getColorDepth()},
		"deviceMemory":        {Value: fg.getDeviceMemory()},
		"screenResolution":    {Value: fg.getScreenResolution()},
		"hardwareConcurrency": {Value: fg.getHardwareConcurrency()},
		"timezone":            {Value: fg.getTimezone()},
		"sessionStorage":      {Value: true},
		"localStorage":        {Value: true},
		"indexedDB":           {Value: true},
		"openDatabase":        {Value: false},
		"cpuClass":            {Value: nil},
		"platform":            {Value: fg.getPlatformInfo()},
		"plugins":             {Value: fg.getPlugins()},
		"touchSupport":        {Value: fg.getTouchSupport()},
		"vendor":              {Value: fg.getVendorInfo()},
		"vendorFlavors":       {Value: []string{}},
		"cookiesEnabled":      {Value: fg.getCookiesEnabled()},
		"colorGamut":          {Value: "srgb"},
		"invertedColors":      {Value: false},
		"forcedColors":        {Value: false},
		"monochrome":          {Value: 0},
		"contrast":            {Value: 0},
		"reducedMotion":       {Value: false},
		"reducedTransparency": {Value: false},
		"hdr":                 {Value: false},
		"math":                {Value: fg.getMathValues()},
		"pdfViewerEnabled":    {Value: true},
		"architecture":        {Value: 240},
		"applePay":            {Value: -1},
		"privateClickMeasurement": {Value: nil},
		"webGlBasics":         {Value: fg.getWebGLInfo()},
		"webGlExtensions":     {Value: fg.getWebGLExtensions()},
	}
	
	return fg.components
}

// componentsToString 将组件转换为字符串用于哈希
func (fg *FingerprintGenerator) componentsToString(components map[string]FingerprintComponent) string {
	var result strings.Builder
	
	// 获取排序的键
	keys := make([]string, 0, len(components))
	for key := range components {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	
	for i, key := range keys {
		component := components[key]
		var valueStr string
		
		if component.Error != "" {
			valueStr = "error"
		} else {
			// 序列化value为JSON字符串
			valueBytes, err := json.Marshal(component.Value)
			if err != nil {
				valueStr = "error"
			} else {
				valueStr = string(valueBytes)
			}
		}
		
		if i > 0 {
			result.WriteString("|")
		}
		
		// 转义特殊字符
		escapedKey := strings.ReplaceAll(strings.ReplaceAll(key, ":", "\\:"), "|", "\\|")
		result.WriteString(fmt.Sprintf("%s:%s", escapedKey, valueStr))
	}
	
	return result.String()
}

// hashComponents 对组件字符串进行哈希
func (fg *FingerprintGenerator) hashComponents(componentsString string) string {
	hash := sha256.Sum256([]byte(componentsString))
	return fmt.Sprintf("%x", hash)[:20]
}

// GenerateFingerprint 生成完整的指纹
func (fg *FingerprintGenerator) GenerateFingerprint() *FingerprintData {
	components := fg.collectComponents()
	componentsString := fg.componentsToString(components)
	visitorID := fg.hashComponents(componentsString)
	
	return &FingerprintData{
		VisitorID:  visitorID,
		Components: components,
		Confidence: 0.995,
	}
}

// GenerateFingerprint 生成指纹的便捷函数
func GenerateFingerprint() *FingerprintData {
	generator := NewFingerprintGenerator()
	return generator.GenerateFingerprint()
}