# 版本发布指南

本项目使用 GitHub Actions 自动构建和发布 Windows 版本。

## 自动发布流程

### 1. 标签发布（推荐）

创建并推送版本标签来触发自动发布：

```bash
# 创建版本标签
git tag v1.0.0

# 推送标签到远程仓库
git push origin v1.0.0
```

### 2. 手动触发

也可以在 GitHub 网页上手动触发发布：
1. 进入项目的 Actions 页面
2. 选择 "Release" 工作流
3. 点击 "Run workflow" 按钮

## 构建产物

每次发布会自动构建以下文件：

- `oh-my-ai2api-windows-amd64.exe` - Windows x64 版本
- `oh-my-ai2api-windows-arm64.exe` - Windows ARM64 版本  
- `oh-my-ai2api-windows.zip` - 包含所有文件的完整压缩包

## 版本命名规范

建议使用语义化版本号：
- `v1.0.0` - 主要版本
- `v1.1.0` - 次要版本（新功能）
- `v1.0.1` - 补丁版本（bug修复）

## 发布前检查清单

在创建新版本之前，请确保：

- [ ] 代码已经过充分测试
- [ ] 更新了 README.md 中的版本信息
- [ ] 更新了 CHANGELOG.md（如果有）
- [ ] 所有 CI 检查都通过
- [ ] 代码已合并到主分支

## 发布后操作

发布完成后：

1. 检查 GitHub Releases 页面确认发布成功
2. 测试下载的可执行文件是否正常工作
3. 更新相关文档和公告

## 故障排除

如果发布失败：

1. 检查 Actions 页面的错误日志
2. 确认 Go 版本兼容性
3. 检查依赖项是否正确
4. 验证构建脚本语法

## 本地构建

如需本地构建 Windows 版本：

```bash
# AMD64 版本
$env:GOOS="windows"
$env:GOARCH="amd64"
go build -ldflags "-s -w" -o oh-my-ai2api-windows-amd64.exe .

# ARM64 版本
$env:GOOS="windows"
$env:GOARCH="arm64"
go build -ldflags "-s -w" -o oh-my-ai2api-windows-arm64.exe .
```

## 构建优化

当前构建使用了以下优化参数：
- `-ldflags "-s -w"` - 减小可执行文件大小
- 缓存 Go 模块以加速构建
- 并行构建多个架构版本
