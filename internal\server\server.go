package server

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
	"oh-my-ai2api/internal/gmi"
	"oh-my-ai2api/internal/zai"
)

// Server HTTP服务器
type Server struct {
	settings *config.Settings
	logger   *common.Logger
	engine   *gin.Engine
}

// NewServer 创建新的服务器
func NewServer(settings *config.Settings, logger *common.Logger) *Server {
	// 设置Gin模式
	if settings.LogLevel != "DEBUG" {
		gin.SetMode(gin.ReleaseMode)
	}

	engine := gin.New()
	
	// 添加中间件
	engine.Use(gin.Recovery())
	engine.Use(common.SetCORSHeaders())
	
	// 添加日志中间件
	if settings.LogLevel == "DEBUG" {
		engine.Use(gin.Logger())
	}

	server := &Server{
		settings: settings,
		logger:   logger,
		engine:   engine,
	}

	server.setupRoutes()
	return server
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 创建Z.AI处理器
	zaiHandler := zai.NewHandler(s.settings, s.logger)

	// 创建GMI处理器
	gmiHandler := gmi.NewHandler(s.settings.GMIConfig, s.settings.ProxyConfig, s.logger)

	// Z.AI路由组
	zaiGroup := s.engine.Group("/zai")
	{
		v1Group := zaiGroup.Group("/v1")
		{
			// 模型列表
			v1Group.GET("/models", zaiHandler.HandleModels)
			// 聊天完成
			v1Group.POST("/chat/completions", zaiHandler.HandleChatCompletions)
		}
	}

	// GMI路由组
	gmiGroup := s.engine.Group("/gmi")
	{
		v1Group := gmiGroup.Group("/v1")
		{
			// 模型列表
			v1Group.GET("/models", gmiHandler.HandleModels)
			// 聊天完成
			v1Group.POST("/chat/completions", gmiHandler.HandleChatCompletions)
		}
	}

	// 健康检查端点
	s.engine.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"service": "oh-my-ai2api",
		})
	})

	// 根路径信息
	s.engine.GET("/", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "Oh My AI2API - Multi AI Proxy Service",
			"version": "1.0.0",
			"endpoints": gin.H{
				"zai_models":  "/zai/v1/models",
				"zai_chat":    "/zai/v1/chat/completions",
				"gmi_models":  "/gmi/v1/models",
				"gmi_chat":    "/gmi/v1/chat/completions",
				"health":      "/health",
			},
		})
	})
}

// Start 启动服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.settings.Host, s.settings.Port)
	
	s.logger.Info("=== Oh My AI2API Server Starting ===")
	s.logger.Info(fmt.Sprintf("Server listening on %s", addr))
	s.logger.Info(fmt.Sprintf("Z.AI endpoints available at /zai/v1/*"))
	s.logger.Info(fmt.Sprintf("GMI endpoints available at /gmi/v1/*"))
	s.logger.Info(fmt.Sprintf("Z.AI Model: %s", s.settings.ModelName))
	s.logger.Info(fmt.Sprintf("Z.AI Upstream: %s", s.settings.UpstreamURL))
	s.logger.Info(fmt.Sprintf("Z.AI Cookies configured: %d", len(s.settings.Cookies)))
	s.logger.Info(fmt.Sprintf("GMI Target: %s", s.settings.GMIConfig.TargetURL))
	s.logger.Info(fmt.Sprintf("GMI Valid Token: %s", s.settings.GMIConfig.ValidToken))
	s.logger.Info(fmt.Sprintf("Log level: %s", s.settings.LogLevel))
	s.logger.Info("=== Server Ready ===")

	return s.engine.Run(addr)
}

// GetEngine 获取Gin引擎（用于测试）
func (s *Server) GetEngine() *gin.Engine {
	return s.engine
}
