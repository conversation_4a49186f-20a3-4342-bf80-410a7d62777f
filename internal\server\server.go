package server

import (
	"fmt"
	"os"

	"github.com/gin-gonic/gin"
	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
	"oh-my-ai2api/internal/deepinfra"
	"oh-my-ai2api/internal/gmi"
	"oh-my-ai2api/internal/gptoss"
	"oh-my-ai2api/internal/you"
	"oh-my-ai2api/internal/zai"
)

// Server HTTP服务器
type Server struct {
	settings    *config.Settings
	logger      *common.Logger
	engine      *gin.Engine
	gptossConfig *gptoss.GptossConfig
}

// NewServer 创建新的服务器
func NewServer(settings *config.Settings, logger *common.Logger) *Server {
	// 设置Gin模式
	if settings.LogLevel != "DEBUG" {
		gin.SetMode(gin.ReleaseMode)
	}

	engine := gin.New()
	
	// 添加中间件
	engine.Use(gin.Recovery())
	engine.Use(common.SetCORSHeaders())
	
	// 添加日志中间件
	if settings.LogLevel == "DEBUG" {
		engine.Use(gin.Logger())
	}

	// 创建Gptoss配置（使用全局HTTP配置）
	gptossConfig := gptoss.ParseGptossConfigWithHTTP(settings.HTTPConfig, settings.ProxyConfig, logger)

	server := &Server{
		settings:    settings,
		logger:      logger,
		engine:      engine,
		gptossConfig: gptossConfig,
	}

	server.setupRoutes()
	return server
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 创建Z.AI处理器
	zaiHandler := zai.NewHandler(s.settings, s.logger)

	// 创建GMI处理器
	gmiHandler := gmi.NewHandler(s.settings.GMIConfig, s.settings.ProxyConfig, s.settings.HTTPConfig, s.logger)

	// 创建Gptoss处理器
	gptossHandler := gptoss.NewHandler(s.gptossConfig, s.settings.HTTPConfig, s.settings.ProxyConfig, s.logger)

	// 创建You处理器
	youHandler := you.NewHandler(s.settings.YouConfig, s.settings.ProxyConfig, s.settings.HTTPConfig, s.logger)

	// 创建Deepinfra处理器
	deepinfraHandler := deepinfra.NewHandler(s.settings.DeepinfraConfig, s.settings.HTTPConfig, s.settings.ProxyConfig, s.logger)

	// Z.AI路由组
	zaiGroup := s.engine.Group("/zai")
	{
		v1Group := zaiGroup.Group("/v1")
		{
			// 模型列表
			v1Group.GET("/models", zaiHandler.HandleModels)
			// 聊天完成
			v1Group.POST("/chat/completions", zaiHandler.HandleChatCompletions)
		}
	}

	// GMI路由组
	gmiGroup := s.engine.Group("/gmi")
	{
		v1Group := gmiGroup.Group("/v1")
		{
			// 模型列表
			v1Group.GET("/models", gmiHandler.HandleModels)
			// 聊天完成
			v1Group.POST("/chat/completions", gmiHandler.HandleChatCompletions)
		}
	}

	// Gptoss路由组
	gptossGroup := s.engine.Group("/gptoss")
	{
		v1Group := gptossGroup.Group("/v1")
		{
			// 模型列表
			v1Group.GET("/models", gptossHandler.HandleModels)
			// 聊天完成
			v1Group.POST("/chat/completions", gptossHandler.HandleChatCompletions)
		}
		
		// 管理端点
		gptossGroup.GET("/refresh-fingerprint", gptossHandler.HandleRefreshFingerprint)
		gptossGroup.GET("/refresh-pkid", gptossHandler.HandleRefreshPkID)
		gptossGroup.GET("/refresh-session-id", gptossHandler.HandleRefreshSessionID)
		gptossGroup.GET("/refresh-all", gptossHandler.HandleRefreshAll)
		gptossGroup.GET("/status", gptossHandler.HandleStatus)
	}

	// You路由组
	youGroup := s.engine.Group("/you")
	{
		v1Group := youGroup.Group("/v1")
		{
			// 模型列表
			v1Group.GET("/models", youHandler.HandleModels)
			// 聊天完成
			v1Group.POST("/chat/completions", youHandler.HandleChatCompletions)
		}
	}

	// Deepinfra路由组
	deepinfraGroup := s.engine.Group("/deepinfra")
	{
		v1Group := deepinfraGroup.Group("/v1")
		{
			// CORS预检请求
			v1Group.OPTIONS("/models", deepinfraHandler.HandleOptions)
			v1Group.OPTIONS("/chat/completions", deepinfraHandler.HandleOptions)

			// 模型列表
			v1Group.GET("/models", deepinfraHandler.HandleModels)
			// 聊天完成
			v1Group.POST("/chat/completions", deepinfraHandler.HandleChatCompletions)
		}

		// 管理端点
		deepinfraGroup.GET("/status", deepinfraHandler.HandleStatus)
	}

	// 健康检查端点
	s.engine.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"service": "oh-my-ai2api",
		})
	})

	// 根路径信息
	s.engine.GET("/", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "Oh My AI2API - Multi AI Proxy Service",
			"version": "1.0.0",
			"endpoints": gin.H{
				"zai_models":           "/zai/v1/models",
				"zai_chat":             "/zai/v1/chat/completions",
				"gmi_models":           "/gmi/v1/models",
				"gmi_chat":             "/gmi/v1/chat/completions",
				"gptoss_models":        "/gptoss/v1/models",
				"gptoss_chat":          "/gptoss/v1/chat/completions",
				"gptoss_refresh_fingerprint": "/gptoss/refresh-fingerprint",
				"gptoss_refresh_pkid":  "/gptoss/refresh-pkid",
				"gptoss_refresh_session_id": "/gptoss/refresh-session-id",
				"gptoss_refresh_all":   "/gptoss/refresh-all",
				"gptoss_status":        "/gptoss/status",
				"you_models":           "/you/v1/models",
				"you_chat":             "/you/v1/chat/completions",
				"deepinfra_models":     "/deepinfra/v1/models",
				"deepinfra_chat":       "/deepinfra/v1/chat/completions",
				"deepinfra_status":     "/deepinfra/status",
				"health":               "/health",
			},
		})
	})
}

// Start 启动服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.settings.Host, s.settings.Port)
	
	s.logger.Info("=== Oh My AI2API Server Starting ===")
	s.logger.Info(fmt.Sprintf("Server listening on %s", addr))
	s.logger.Info(fmt.Sprintf("Z.AI endpoints available at /zai/v1/*"))
	s.logger.Info(fmt.Sprintf("GMI endpoints available at /gmi/v1/*"))
	s.logger.Info(fmt.Sprintf("Gptoss endpoints available at /gptoss/v1/*"))
	s.logger.Info(fmt.Sprintf("You.com endpoints available at /you/v1/*"))
	s.logger.Info(fmt.Sprintf("Deepinfra endpoints available at /deepinfra/v1/*"))
	s.logger.Info(fmt.Sprintf("Z.AI Model: %s", s.settings.ModelName))
	s.logger.Info(fmt.Sprintf("Z.AI Upstream: %s", s.settings.UpstreamURL))
	s.logger.Info(fmt.Sprintf("Z.AI Cookies configured: %d", len(s.settings.Cookies)))
	s.logger.Info(fmt.Sprintf("GMI Target: %s", s.settings.GMIConfig.TargetURL))
	s.logger.Info(fmt.Sprintf("GMI Valid Token: %s", s.settings.GMIConfig.ValidToken))
	s.logger.Info(fmt.Sprintf("Gptoss Session ID configured: %s", func() string {
		if s.gptossConfig.SessionID != "" {
			return "✓"
		}
		return "✗"
	}()))
	s.logger.Info(fmt.Sprintf("Gptoss Fingerprint configured: %s", func() string {
		if s.gptossConfig.Fingerprint != "" {
			return "✓"
		}
		return "✗"
	}()))
	
	// 如果指纹是自动生成的，记录相关信息
	if s.gptossConfig.Fingerprint != "" && os.Getenv("GPTOSS_FINGERPRINT") == "" {
		s.logger.Info("🔄 Gptoss Fingerprint auto-generated (not configured in environment)")
		s.logger.Info(fmt.Sprintf("   Generated Fingerprint: %s", s.gptossConfig.Fingerprint))
		s.logger.Info(fmt.Sprintf("   Confidence: %.3f", s.gptossConfig.FingerprintData.Confidence))
	}
	
	// 如果PkID是自动生成的，记录相关信息
	if s.gptossConfig.PkID != "" && os.Getenv("GPTOSS_PKID") == "" {
		s.logger.Info("🔄 Gptoss PkID auto-generated (not configured in environment)")
		s.logger.Info(fmt.Sprintf("   Generated PkID: %s", s.gptossConfig.PkID))
	}
	
	// 如果SessionID是自动获取的，记录相关信息
	if s.gptossConfig.SessionID != "" && os.Getenv("GPTOSS_SESSION_ID") == "" {
		s.logger.Info("🔄 Gptoss SessionID auto-fetched (not configured in environment)")
		s.logger.Info(fmt.Sprintf("   Fetched SessionID: %s", s.gptossConfig.SessionID))
	} else if s.gptossConfig.SessionID == "" && os.Getenv("GPTOSS_SESSION_ID") == "" {
		s.logger.Info("⚠️  Gptoss SessionID auto-fetch failed (will use empty value)")
	}

	// Deepinfra配置状态
	s.logger.Info(fmt.Sprintf("Deepinfra Enabled: %s", func() string {
		if s.settings.DeepinfraConfig.Enabled {
			return "✓"
		}
		return "✗"
	}()))
	s.logger.Info(fmt.Sprintf("Deepinfra Target: %s", s.settings.DeepinfraConfig.TargetURL))
	s.logger.Info(fmt.Sprintf("Deepinfra Auth: %s", func() string {
		if s.settings.DeepinfraConfig.Token != "" {
			return "✓"
		}
		return "✗"
	}()))

	s.logger.Info(fmt.Sprintf("Log level: %s", s.settings.LogLevel))
	s.logger.Info("=== Server Ready ===")

	return s.engine.Run(addr)
}

// GetEngine 获取Gin引擎（用于测试）
func (s *Server) GetEngine() *gin.Engine {
	return s.engine
}
