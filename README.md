# Oh My AI2API - Multi AI Proxy

基于Golang实现的多AI服务代理，提供OpenAI兼容的API接口。支持Z.AI和GMI Cloud等多个AI服务。

## 功能特性

- 🚀 OpenAI兼容的API接口
- 🔄 支持流式和非流式响应
- 🤖 多AI服务支持：
  - **Z.AI**: 智能Cookie管理和轮询，内容转换（thinking标签处理）
  - **GMI Cloud**: 固定token认证，随机User-Agent轮换
- 🔐 灵活的认证机制（本地API Key或透传上游认证）
- 🌐 代理支持（HTTP/HTTPS）
- 📊 健康检查和自动恢复
- 📝 详细的请求/响应日志记录
- 📁 **企业级日志功能**：文件输出、自动轮转、压缩备份
- 🔧 可扩展架构，便于添加更多AI服务

## 快速开始

### 1. 配置环境变量

复制示例配置文件：
```bash
cp .env.example .env
```

编辑`.env`文件，设置必要的配置：
```bash
# Z.AI配置（如需使用Z.AI服务）
Z_AI_COOKIES=your_z_ai_cookie_1,your_z_ai_cookie_2

# GMI配置（如需使用GMI服务）
GMI_VALID_TOKEN=gmi-free-2-api

# 通用配置
API_KEY=sk-your-custom-key
PORT=8000
LOG_LEVEL=INFO

# 日志文件配置（可选）
LOG_ENABLE_FILE=true
LOG_FILE_PATH=logs/oh-my-ai2api.log
LOG_MAX_FILE_SIZE=100
```

### 2. 编译运行

```bash
# 下载依赖
go mod tidy

# 编译
go build -o oh-my-ai2api.exe .

# 运行
./oh-my-ai2api.exe
```

### 3. 使用API

服务启动后，多个AI服务的端点将可用：

#### Z.AI服务（/zai路径下）

获取Z.AI模型列表：
```bash
curl http://localhost:8000/zai/v1/models \
  -H "Authorization: Bearer sk-z2api-key-2024"
```

Z.AI聊天完成：
```bash
curl http://localhost:8000/zai/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-z2api-key-2024" \
  -d '{
    "model": "GLM-4.5",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ],
    "stream": false
  }'
```

#### GMI Cloud服务（/gmi路径下）

获取GMI模型列表：
```bash
curl http://localhost:8000/gmi/v1/models \
  -H "Authorization: Bearer gmi-free-2-api"
```

GMI聊天完成：
```bash
curl http://localhost:8000/gmi/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer gmi-free-2-api" \
  -d '{
    "model": "Qwen/Qwen3-32B-FP8",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ],
    "stream": false
  }'
```

## API端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 服务信息 |
| `/health` | GET | 健康检查 |
| `/zai/v1/models` | GET | 获取Z.AI模型列表 |
| `/zai/v1/chat/completions` | POST | Z.AI聊天完成 |
| `/gmi/v1/models` | GET | 获取GMI模型列表 |
| `/gmi/v1/chat/completions` | POST | GMI聊天完成 |

## 配置说明

| 环境变量 | 默认值 | 描述 |
|----------|--------|------|
| `HOST` | `0.0.0.0` | 服务器监听地址 |
| `PORT` | `8000` | 服务器端口 |
| `API_KEY` | `sk-z2api-key-2024` | 本地API密钥 |
| `Z_AI_COOKIES` | - | Z.AI认证cookies（Z.AI服务必需） |
| `SHOW_THINK_TAGS` | `false` | 是否显示思考标签（Z.AI） |
| `DEFAULT_STREAM` | `false` | 默认是否使用流式响应 |
| `GMI_VALID_TOKEN` | `gmi-free-2-api` | GMI认证token |
| `GMI_TARGET_URL` | `https://console.gmicloud.ai/chat` | GMI目标URL |
| `GMI_MODELS_URL` | `https://api.gmi-serving.com/v1/models` | GMI模型API URL |
| `LOG_LEVEL` | `INFO` | 日志级别 |
| `STREAM_TIMEOUT` | `30` | 流式响应超时时间（秒） |

## 认证机制

### Z.AI服务认证
1. **本地认证**：当请求的Authorization header匹配配置的`API_KEY`时，使用本地cookie池进行Z.AI认证
2. **透传认证**：当Authorization header不匹配本地API_KEY时，直接透传给Z.AI进行认证

### GMI服务认证
- 使用固定token `gmi-free-2-api` 进行认证
- 内部调用GMI API时使用配置的Bearer token

## 架构设计

项目采用分层架构，便于扩展：

```
oh-my-ai2api/
├── main.go                 # 主入口
├── internal/
│   ├── config/            # 配置管理
│   ├── common/            # 通用工具
│   ├── zai/               # Z.AI相关逻辑
│   ├── gmi/               # GMI相关逻辑
│   └── server/            # HTTP服务器
```

## 日志功能

### 📁 企业级日志管理

支持完整的日志文件输出和管理功能：

**核心特性：**
- ✅ 同时输出到控制台和文件
- ✅ 自动日志轮转（基于文件大小）
- ✅ 自动压缩旧日志文件
- ✅ 可配置备份文件数量和保留天数
- ✅ 向后兼容，默认禁用

**配置参数：**

| 环境变量 | 默认值 | 说明 |
|----------|--------|------|
| `LOG_ENABLE_FILE` | `false` | 启用文件日志 |
| `LOG_FILE_PATH` | `logs/oh-my-ai2api.log` | 日志文件路径 |
| `LOG_MAX_FILE_SIZE` | `100` | 最大文件大小(MB) |
| `LOG_MAX_BACKUPS` | `3` | 最大备份文件数 |
| `LOG_MAX_AGE` | `28` | 最大保留天数 |
| `LOG_COMPRESS` | `true` | 压缩旧日志文件 |

**使用示例：**
```bash
# 启用日志文件输出
LOG_ENABLE_FILE=true
LOG_FILE_PATH=logs/app.log
LOG_MAX_FILE_SIZE=50
LOG_MAX_BACKUPS=5
```

详细说明请参考：[LOG_FILE_FEATURE.md](LOG_FILE_FEATURE.md)

## 开发说明

- 使用Gin框架提供HTTP服务
- 支持CORS跨域请求
- 全局错误处理，不在controller中使用try-catch
- 使用普通注释，不使用Swagger注解
- 遵循Go语言最佳实践

## 许可证

MIT License
