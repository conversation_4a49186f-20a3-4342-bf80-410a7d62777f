package zai

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
)

// Handler Z.AI处理器
type Handler struct {
	settings     *config.Settings
	proxyHandler *ProxyHandler
	logger       *common.Logger
}

// NewHandler 创建新的处理器
func NewHandler(settings *config.Settings, logger *common.Logger) *Handler {
	return &Handler{
		settings:     settings,
		proxyHandler: NewProxyHandler(settings, logger),
		logger:       logger,
	}
}

// HandleModels 处理模型列表请求
// GET /zai/v1/models
func (h *Handler) HandleModels(c *gin.Context) {
	models := ModelsResponse{
		Object: "list",
		Data: []Model{{
			ID:      h.settings.ModelID,
			Object:  "model",
			OwnedBy: "z-ai",
		}},
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, models)
}

// HandleChatCompletions 处理聊天完成请求
// POST /zai/v1/chat/completions
func (h *Handler) HandleChatCompletions(c *gin.Context) {
	h.logger.Debug("=== HandleChatCompletions Started ===")

	// 验证认证
	authResult := common.VerifyAuth(c.GetHeader("Authorization"), h.settings.APIKey)
	if !authResult.Valid {
		h.logger.Warning("Authentication failed: " + authResult.Error)
		common.SendUnauthorized(c, authResult.Error)
		return
	}

	h.logger.Debug(fmt.Sprintf("Authentication successful. Use local auth: %v", authResult.UseLocalAuth))

	// 解析请求体
	var request ChatCompletionRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("Failed to parse request body: " + err.Error())
		common.SendBadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// 打印请求参数
	h.logger.Info("=== ZAI Chat Completions Request Parameters ===")
	if requestJSON, err := json.Marshal(request); err == nil {
		h.logger.Info("Request: " + string(requestJSON))
	} else {
		h.logger.Error("Failed to marshal request: " + err.Error())
	}

	// 如果使用本地认证，检查cookies是否配置
	if authResult.UseLocalAuth && len(h.settings.Cookies) == 0 {
		h.logger.Error("No Z.AI cookies configured for local authentication")
		common.SendServiceUnavailable(c, "Service unavailable: No Z.AI cookies configured. Please set Z_AI_COOKIES environment variable.")
		return
	}

	// 验证模型
	if err := common.ValidateModel(request.Model, h.settings.ModelName); err != nil {
		h.logger.Error("Model validation failed: " + err.Error())
		common.SendBadRequest(c, err.Error())
		return
	}

	h.logger.Debug("Model validation passed")

	// 构建认证信息
	auth := &AuthInfo{
		UseLocalAuth: authResult.UseLocalAuth,
		UpstreamAuth: authResult.UpstreamAuth,
	}

	// 调用代理处理器
	h.logger.Debug("Calling proxyHandler.HandleChatCompletion...")
	result, err := h.proxyHandler.HandleChatCompletion(&request, auth)
	if err != nil {
		h.logger.Error("ProxyHandler error: " + err.Error())
		h.handleProxyError(c, err)
		return
	}

	// 处理响应
	h.logger.Debug("Processing response...")
	if chunks, ok := result.([]string); ok {
		// 流式响应
		h.logger.Debug(fmt.Sprintf("Sending streaming response with %d chunks", len(chunks)))
		h.sendStreamingResponse(c, chunks)
	} else if response, ok := result.(*ChatCompletionResponse); ok {
		// 非流式响应
		h.logger.Debug("Sending non-streaming response")
		c.JSON(http.StatusOK, response)
	} else {
		h.logger.Error("Unknown response type")
		common.SendInternalError(c, "Unknown response type")
		return
	}

	h.logger.Debug("=== Chat Completions Request Completed Successfully ===")
}

// sendStreamingResponse 发送流式响应
func (h *Handler) sendStreamingResponse(c *gin.Context, chunks []string) {
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")

	c.Status(http.StatusOK)

	// 发送所有chunks
	for _, chunk := range chunks {
		c.Writer.WriteString(chunk)
		c.Writer.Flush()
	}
}

// handleProxyError 处理代理错误
func (h *Handler) handleProxyError(c *gin.Context, err error) {
	errorMsg := err.Error()
	
	switch {
	case contains(errorMsg, "No available cookies"):
		h.logger.Error("Error type: No available cookies")
		common.SendServiceUnavailable(c, errorMsg)
	case contains(errorMsg, "Invalid authentication"):
		h.logger.Error("Error type: Invalid authentication")
		common.SendUnauthorized(c, errorMsg)
	case contains(errorMsg, "not supported"):
		h.logger.Error("Error type: Model not supported")
		common.SendBadRequest(c, errorMsg)
	default:
		h.logger.Error("Error type: Internal server error")
		common.SendInternalError(c, errorMsg)
	}
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(s) > len(substr) && 
		(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || 
		 findSubstring(s, substr)))
}

// findSubstring 查找子字符串
func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
