# Oh My AI2API - Multi AI Proxy Configuration

# 服务器配置
HOST=0.0.0.0
PORT=8000

# API密钥（用于本地认证）
API_KEY=sk-z2api-key-2024

# Z.AI Cookies（必需，用逗号分隔多个cookie）
Z_AI_COOKIES=your_z_ai_cookie_here

# 显示思考标签（true/false）
SHOW_THINK_TAGS=false

# 默认流式响应（true/false）
DEFAULT_STREAM=false

# 每分钟最大请求数
MAX_REQUESTS_PER_MINUTE=60

# 日志级别（DEBUG/INFO/WARNING/ERROR）
LOG_LEVEL=INFO

# 流式响应超时时间（秒）
STREAM_TIMEOUT=30

# 代理配置（可选）
# HTTP_PROXY=http://proxy.example.com:8080
# HTTPS_PROXY=https://proxy.example.com:8080

# 禁用TLS验证（不推荐在生产环境使用）
# NODE_TLS_REJECT_UNAUTHORIZED=false

# 禁用.env文件加载
# DISABLE_DOTENV=false

# ==================== GMI配置 ====================
# GMI认证token
GMI_VALID_TOKEN=gmi-free-2-api

# GMI目标URL
GMI_TARGET_URL=https://console.gmicloud.ai/chat

# GMI模型API URL
GMI_MODELS_URL=https://api.gmi-serving.com/v1/models

# GMI Bearer Token（用于调用GMI API）
GMI_BEARER_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImNhNGNkNGU1LTMyY2YtNDQ5OC1hNDZiLTFiYjFmMzI3NTUzMiIsInNjb3BlIjoiaWVfbW9kZWwiLCJjbGllbnRJZCI6IjAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCJ9.TTdQWMVpyx55Zb0oWqWcny1aYAl7yc_ctNmIphkkBfw
