# Oh My AI2API - Multi AI Proxy Configuration

# 服务器配置
HOST=0.0.0.0
PORT=8000

# API密钥（用于本地认证）
API_KEY=sk-z2api-key-2024

# Z.AI Cookies（必需，用逗号分隔多个cookie）
Z_AI_COOKIES=your_z_ai_cookie_here

# 显示思考标签（true/false）
SHOW_THINK_TAGS=false

# 默认流式响应（true/false）
DEFAULT_STREAM=false

# 每分钟最大请求数
MAX_REQUESTS_PER_MINUTE=60

# 日志级别（DEBUG/INFO/WARNING/ERROR）
LOG_LEVEL=INFO

# 流式响应超时时间（秒）
STREAM_TIMEOUT=30

# ==================== 日志文件配置 ====================
# 启用日志文件输出（true/false）
LOG_ENABLE_FILE=false

# 日志文件路径
LOG_FILE_PATH=logs/oh-my-ai2api.log

# 日志文件最大大小（MB）
LOG_MAX_FILE_SIZE=100

# 最大备份文件数
LOG_MAX_BACKUPS=3

# 日志文件最大保留天数
LOG_MAX_AGE=28

# 是否压缩旧日志文件（true/false）
LOG_COMPRESS=true

# ==================== 代理配置 ====================
# HTTP代理配置（可选）
# 支持以下格式：
# 1. 基本代理: http://proxy.example.com:8080
# 2. 带认证的代理: http://username:<EMAIL>:8080
# 3. HTTPS代理: https://proxy.example.com:443
# HTTP_PROXY=http://proxy.example.com:8080
# HTTPS_PROXY=https://proxy.example.com:8080
#
# 带认证的代理示例：
# HTTP_PROXY=http://username:<EMAIL>:8080

# ==================== HTTP客户端配置 ====================
# 跳过SSL证书验证（true/false）
# 警告：仅在开发/测试环境使用，生产环境不建议启用
HTTP_INSECURE_SKIP_VERIFY=false

# 禁用TLS验证（不推荐在生产环境使用）
# NODE_TLS_REJECT_UNAUTHORIZED=false

# 禁用.env文件加载
# DISABLE_DOTENV=false

# ==================== GMI配置 ====================
# GMI认证token
GMI_VALID_TOKEN=gmi-free-2-api

# GMI目标URL
GMI_TARGET_URL=https://console.gmicloud.ai/chat

# GMI模型API URL
GMI_MODELS_URL=https://api.gmi-serving.com/v1/models

# GMI Bearer Token（用于调用GMI API）
GMI_BEARER_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImNhNGNkNGU1LTMyY2YtNDQ5OC1hNDZiLTFiYjFmMzI3NTUzMiIsInNjb3BlIjoiaWVfbW9kZWwiLCJjbGllbnRJZCI6IjAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCJ9.TTdQWMVpyx55Zb0oWqWcny1aYAl7yc_ctNmIphkkBfw

# ==================== Deepinfra 配置 ====================
# 启用Deepinfra服务（true/false）
DEEPINFRA_ENABLED=false

# Deepinfra API Token（可选）
# 如果设置，将启用Token验证
DEEPINFRA_TOKEN=

# Deepinfra目标URL（可选）
# 默认: https://api.deepinfra.com/v1/openai/chat/completions
DEEPINFRA_TARGET_URL=https://api.deepinfra.com/v1/openai/chat/completions
