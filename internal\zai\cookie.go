package zai

import (
	"bytes"
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"oh-my-ai2api/internal/common"
)

// CookieManager Cookie管理器
type CookieManager struct {
	cookies       []string
	currentIndex  int
	failedCookies map[string]bool
	mutex         sync.RWMutex
	logger        *common.Logger
	upstreamURL   string
	upstreamModel string
}

// NewCookieManager 创建新的Cookie管理器
func NewCookieManager(cookies []string, logger *common.Logger, upstreamURL, upstreamModel string) *CookieManager {
	cm := &CookieManager{
		cookies:       cookies,
		currentIndex:  0,
		failedCookies: make(map[string]bool),
		logger:        logger,
		upstreamURL:   upstreamURL,
		upstreamModel: upstreamModel,
	}

	if len(cookies) > 0 {
		cm.logger.Info("Initialized CookieManager with " + string(rune(len(cookies))) + " cookies")
	} else {
		cm.logger.Warning("CookieManager initialized with no cookies")
	}

	// 启动健康检查
	cm.startHealthCheck()
	return cm
}

// GetNextCookie 获取下一个可用的cookie
func (cm *CookieManager) GetNextCookie() string {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if len(cm.cookies) == 0 {
		return ""
	}

	attempts := 0
	for attempts < len(cm.cookies) {
		cookie := cm.cookies[cm.currentIndex]
		cm.currentIndex = (cm.currentIndex + 1) % len(cm.cookies)

		if !cm.failedCookies[cookie] {
			return cookie
		}
		attempts++
	}

	// 所有cookie都失败了，重置失败集合并重试
	if len(cm.failedCookies) > 0 {
		cm.logger.Warning("All cookies failed, resetting failed set and retrying")
		cm.failedCookies = make(map[string]bool)
		if len(cm.cookies) > 0 {
			return cm.cookies[0]
		}
	}

	return ""
}

// MarkCookieFailed 标记cookie为失败
func (cm *CookieManager) MarkCookieFailed(cookie string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.failedCookies[cookie] = true
	if len(cookie) > 20 {
		cm.logger.Warning("Marked cookie as failed: " + cookie[:20] + "...")
	} else {
		cm.logger.Warning("Marked cookie as failed: " + cookie)
	}
}

// MarkCookieSuccess 标记cookie为成功
func (cm *CookieManager) MarkCookieSuccess(cookie string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cm.failedCookies[cookie] {
		delete(cm.failedCookies, cookie)
		if len(cookie) > 20 {
			cm.logger.Info("Cookie recovered: " + cookie[:20] + "...")
		} else {
			cm.logger.Info("Cookie recovered: " + cookie)
		}
	}
}

// healthCheck 健康检查单个cookie
func (cm *CookieManager) healthCheck(cookie string) bool {
	// 构建测试请求
	testData := ZAIRequest{
		Stream:    false,
		Model:     cm.upstreamModel,
		Messages:  []ZAIMessage{{Role: "user", Content: "test"}},
		ChatID:    common.GenerateUUID(),
		ID:        common.GenerateUUID(),
		ModelItem: ZAIModelItem{ID: cm.upstreamModel, Name: "GLM-4.5", OwnedBy: "openai"},
		BackgroundTasks: map[string]bool{
			"title_generation": true,
			"tags_generation":  true,
		},
		Features: map[string]bool{
			"image_generation": false,
			"code_interpreter": false,
			"web_search":       false,
			"auto_web_search":  false,
		},
		MCPServers:  []string{"deep-web-search"},
		Params:      make(map[string]interface{}),
		ToolServers: []string{},
		Variables: map[string]string{
			"{{USER_NAME}}":        "User",
			"{{USER_LOCATION}}":    "Unknown",
			"{{CURRENT_DATETIME}}": time.Now().Format("2006-01-02 15:04:05"),
		},
	}

	jsonData, err := json.Marshal(testData)
	if err != nil {
		return false
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", cm.upstreamURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return false
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+cookie)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == 200
}

// startHealthCheck 启动健康检查
func (cm *CookieManager) startHealthCheck() {
	go func() {
		ticker := time.NewTicker(10 * time.Minute) // 10分钟检查一次
		defer ticker.Stop()

		for range ticker.C {
			cm.mutex.RLock()
			failedCount := len(cm.failedCookies)
			failedCookies := make([]string, 0, failedCount)
			for cookie := range cm.failedCookies {
				failedCookies = append(failedCookies, cookie)
			}
			cm.mutex.RUnlock()

			if failedCount > 0 {
				cm.logger.Info("Running health check for failed cookies")
				for _, cookie := range failedCookies {
					if cm.healthCheck(cookie) {
						cm.MarkCookieSuccess(cookie)
					}
				}
			}
		}
	}()
}
