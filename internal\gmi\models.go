package gmi

import "oh-my-ai2api/internal/common"

// ChatCompletionRequest OpenAI聊天完成请求
type ChatCompletionRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	Stream      *bool     `json:"stream,omitempty"`
	Temperature *float64  `json:"temperature,omitempty"`
	MaxTokens   *int      `json:"max_tokens,omitempty"`
	TopP        *float64  `json:"top_p,omitempty"`
}

// Message 消息 - 支持字符串和数组两种content格式
type Message struct {
	Role    string                `json:"role"`
	Content common.MessageContent `json:"content"`
}

// ChatCompletionResponse OpenAI聊天完成响应
type ChatCompletionResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

// Choice 选择
type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message,omitempty"`
	Delta        *Delta  `json:"delta,omitempty"`
	FinishReason string  `json:"finish_reason"`
}

// Delta 增量内容
type Delta struct {
	Content string `json:"content,omitempty"`
}

// Usage 使用情况
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// ChatCompletionChunk 流式响应块
type ChatCompletionChunk struct {
	ID      string        `json:"id"`
	Object  string        `json:"object"`
	Created int64         `json:"created"`
	Model   string        `json:"model"`
	Choices []ChunkChoice `json:"choices"`
}

// ChunkChoice 流式选择
type ChunkChoice struct {
	Index        int     `json:"index"`
	Delta        Delta   `json:"delta"`
	FinishReason *string `json:"finish_reason"`
}

// ModelsResponse 模型列表响应
type ModelsResponse struct {
	Object string  `json:"object"`
	Data   []Model `json:"data"`
}

// Model 模型信息
type Model struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	OwnedBy string `json:"owned_by"`
}

// GMIMessage GMI内部使用的简单消息格式
type GMIMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// GMIRequest GMI请求格式
type GMIRequest struct {
	Temperature *float64    `json:"temperature,omitempty"`
	MaxTokens   *int        `json:"max_tokens,omitempty"`
	TopP        *float64    `json:"top_p,omitempty"`
	Stream      bool        `json:"stream"`
	Messages    []GMIMessage `json:"messages"`
	Model       string      `json:"model"`
}

// GMIResponse GMI响应格式
type GMIResponse struct {
	Choices []GMIChoice `json:"choices,omitempty"`
	Result  string      `json:"result,omitempty"`
	Usage   Usage       `json:"usage,omitempty"`
}

// GMIChoice GMI选择
type GMIChoice struct {
	Index        int         `json:"index"`
	Message      GMIMessage  `json:"message"`
	FinishReason string      `json:"finish_reason"`
}

// AuthInfo 认证信息
type AuthInfo struct {
	Token string
}
