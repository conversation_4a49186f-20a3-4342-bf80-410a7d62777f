package deepinfra

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
)

// Handler Deepinfra处理器
type Handler struct {
	config      *config.DeepinfraConfig
	proxy       *Proxy
	logger      *common.Logger
	httpConfig  *config.HTTPConfig
	proxyConfig *config.ProxyConfig
}

// NewHandler 创建新的Deepinfra处理器
func NewHandler(deepinfraConfig *config.DeepinfraConfig, httpConfig *config.HTTPConfig, proxyConfig *config.ProxyConfig, logger *common.Logger) *Handler {
	return &Handler{
		config:      deepinfraConfig,
		proxy:       NewProxy(deepinfraConfig, httpConfig, proxyConfig, logger),
		logger:      logger,
		httpConfig:  httpConfig,
		proxyConfig: proxyConfig,
	}
}

// HandleModels 处理模型列表请求
func (h *Handler) HandleModels(c *gin.Context) {
	h.logger.Debug("=== Deepinfra HandleModels Started ===")

	// 验证Token（如果配置了）
	if h.config.Token != "" {
		if err := h.validateToken(c); err != nil {
			h.logger.Error(fmt.Sprintf("Token validation failed: %v", err))
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			return
		}
	}

	response := ModelsResponse{
		Object: "list",
		Data:   h.config.Models,
	}

	c.JSON(http.StatusOK, response)
	h.logger.Debug("=== Deepinfra HandleModels Completed ===")
}

// HandleChatCompletions 处理聊天完成请求
func (h *Handler) HandleChatCompletions(c *gin.Context) {
	h.logger.Debug("=== Deepinfra HandleChatCompletions Started ===")

	// 验证Token（如果配置了）
	if h.config.Token != "" {
		if err := h.validateToken(c); err != nil {
			h.logger.Error(fmt.Sprintf("Token validation failed: %v", err))
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			return
		}
	}

	// 解析请求
	var request ChatCompletionRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error(fmt.Sprintf("Failed to parse request: %v", err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 验证请求
	if err := h.proxy.ValidateRequest(&request); err != nil {
		h.logger.Error(fmt.Sprintf("Request validation failed: %v", err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.logger.Debug(fmt.Sprintf("Processing request for model: %s", request.Model))

	// 重新序列化请求体
	requestBody, err := json.Marshal(request)
	if err != nil {
		h.logger.Error(fmt.Sprintf("Failed to marshal request: %v", err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process request"})
		return
	}

	// 代理请求
	resp, err := h.proxy.ProxyRequest(requestBody)
	if err != nil {
		h.logger.Error(fmt.Sprintf("Proxy request failed: %v", err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to proxy request"})
		return
	}
	defer resp.Body.Close()

	// 复制响应头
	h.proxy.CopyResponseHeaders(resp.Header, c.Writer.Header())

	// 设置状态码
	c.Status(resp.StatusCode)

	// 复制响应体
	if err := h.proxy.CopyResponseBody(resp.Body, c.Writer); err != nil {
		h.logger.Error(fmt.Sprintf("Failed to copy response body: %v", err))
		return
	}

	h.logger.Debug("=== Deepinfra HandleChatCompletions Completed ===")
}

// HandleOptions 处理CORS预检请求
func (h *Handler) HandleOptions(c *gin.Context) {
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS")
	c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
	c.Header("Access-Control-Max-Age", "86400")
	c.Status(http.StatusNoContent)
}

// validateToken 验证Token
func (h *Handler) validateToken(c *gin.Context) error {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		return fmt.Errorf("missing authorization header")
	}

	expectedToken := fmt.Sprintf("Bearer %s", h.config.Token)
	if authHeader != expectedToken {
		return fmt.Errorf("invalid token")
	}

	return nil
}

// HandleStatus 处理状态检查请求
func (h *Handler) HandleStatus(c *gin.Context) {
	status := gin.H{
		"status":     "running",
		"service":    "deepinfra",
		"target_url": h.config.TargetURL,
		"models":     len(h.config.Models),
		"endpoints": gin.H{
			"chat_completions": "/v1/chat/completions",
			"models":          "/v1/models",
			"status":          "/status",
		},
	}

	// 如果配置了Token，显示Token状态
	if h.config.Token != "" {
		status["auth"] = "enabled"
	} else {
		status["auth"] = "disabled"
	}

	c.JSON(http.StatusOK, status)
}
