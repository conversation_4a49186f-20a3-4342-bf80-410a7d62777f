# fingerprint_generator.py
"""
Fingerprint generator based on FingerprintJS library
Replicates the fingerprint generation logic used by chat-gpt-oss.com
"""

import hashlib
import json
import platform
import time
import random
import subprocess
import re
from typing import Dict, Any, List, Optional, Union

class FingerprintGenerator:
    def __init__(self):
        self.components = {}
        
    def get_screen_resolution(self):
        """Get screen resolution"""
        try:
            # Try to get screen resolution on Windows
            import tkinter as tk
            root = tk.Tk()
            width = root.winfo_screenwidth()
            height = root.winfo_screenheight()
            root.destroy()
            return [width, height]
        except:
            return [1920, 1080]  # Default fallback
    
    def get_timezone(self):
        """Get timezone offset"""
        try:
            import time
            return time.timezone // 60
        except:
            return -480  # Default PST
    
    def get_languages(self):
        """Get system languages"""
        try:
            import locale
            lang = locale.getdefaultlocale()[0]
            return [lang] if lang else ["en-US"]
        except:
            return ["en-US"]
    
    def get_platform_info(self):
        """Get platform information"""
        return platform.platform()
    
    def get_hardware_concurrency(self):
        """Get number of CPU cores"""
        try:
            import multiprocessing
            return multiprocessing.cpu_count()
        except:
            return 4
    
    def get_color_depth(self):
        """Get color depth"""
        return 24  # Standard 24-bit color
    
    def get_device_memory(self):
        """Get device memory (approximation)"""
        try:
            import psutil
            return round(psutil.virtual_memory().total / (1024**3))
        except:
            return 8  # Default 8GB
    
    def get_canvas_fingerprint(self):
        """Generate canvas fingerprint (simplified)"""
        # This is a simplified version - real canvas fingerprinting is more complex
        text = "mmMwWLliI0fiflO&1"
        # Simulate canvas rendering differences with some randomness
        random_component = random.randint(1000, 9999)
        canvas_data = f"canvas_{platform.system()}_{self.get_hardware_concurrency()}_{random_component}"
        return hashlib.md5(canvas_data.encode()).hexdigest()[:16]
    
    def get_webgl_info(self):
        """Get WebGL information (simplified)"""
        return {
            "vendor": "Google Inc. (Intel)",
            "renderer": "ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)",
            "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)"
        }
    
    def get_audio_fingerprint(self):
        """Get audio context fingerprint (simplified)"""
        return 44100  # Standard sample rate
    
    def get_fonts(self):
        """Get available fonts (simplified)"""
        # This would normally check system fonts
        return ["Arial", "Helvetica", "Times New Roman", "Courier New"]
    
    def get_plugins(self):
        """Get browser plugins (simplified)"""
        # Browser plugins - simplified for this implementation
        return []
    
    def get_touch_support(self):
        """Get touch support info"""
        return [0, False, False]  # [maxTouchPoints, touchEvent, touchStart]
    
    def get_vendor_info(self):
        """Get vendor information"""
        return "Google Inc."
    
    def get_cookies_enabled(self):
        """Check if cookies are enabled"""
        return True
    
    def get_storage_info(self):
        """Get storage information"""
        return {
            "sessionStorage": True,
            "localStorage": True,
            "indexedDB": True,
            "openDatabase": False
        }
    
    def collect_components(self):
        """Collect all fingerprint components"""
        self.components = {
            "fonts": {"value": self.get_fonts()},
            "domBlockers": {"value": []},
            "fontPreferences": {"value": {"default": 120, "apple": 120, "serif": 120, "sans": 120, "mono": 120, "min": 120, "system": 120}},
            "audio": {"value": self.get_audio_fingerprint()},
            "screenFrame": {"value": [0, 0, 0, 0]},
            "canvas": {"value": self.get_canvas_fingerprint()},
            "osCpu": {"value": platform.processor()},
            "languages": {"value": self.get_languages()},
            "colorDepth": {"value": self.get_color_depth()},
            "deviceMemory": {"value": self.get_device_memory()},
            "screenResolution": {"value": self.get_screen_resolution()},
            "hardwareConcurrency": {"value": self.get_hardware_concurrency()},
            "timezone": {"value": self.get_timezone()},
            "sessionStorage": {"value": True},
            "localStorage": {"value": True},
            "indexedDB": {"value": True},
            "openDatabase": {"value": False},
            "cpuClass": {"value": None},
            "platform": {"value": self.get_platform_info()},
            "plugins": {"value": self.get_plugins()},
            "touchSupport": {"value": self.get_touch_support()},
            "vendor": {"value": self.get_vendor_info()},
            "vendorFlavors": {"value": []},
            "cookiesEnabled": {"value": self.get_cookies_enabled()},
            "colorGamut": {"value": "srgb"},
            "invertedColors": {"value": False},
            "forcedColors": {"value": False},
            "monochrome": {"value": 0},
            "contrast": {"value": 0},
            "reducedMotion": {"value": False},
            "reducedTransparency": {"value": False},
            "hdr": {"value": False},
            "math": {"value": {
                "acos": 1.4473588658278522,
                "acosh": 709.889355822726,
                "acoshPf": 355.291251501643,
                "asin": 0.12343746096704435,
                "asinh": 0.881373587019543,
                "asinhPf": 0.8813735870195429,
                "atanh": 0.5493061443340548,
                "atanhPf": 0.5493061443340548,
                "atan": 0.4636476090008061,
                "sin": 0.8939966636005579,
                "sinh": 1.1752011936438014,
                "sinhPf": 1.1752011936438014,
                "cos": -0.4480736161291701,
                "cosh": 1.5430806348152437,
                "coshPf": 1.5430806348152437,
                "tan": -1.995636353420487,
                "tanh": 0.7615941559557649,
                "tanhPf": 0.7615941559557649,
                "exp": 2.718281828459045,
                "expm1": 1.718281828459045,
                "expm1Pf": 1.7182818284590451,
                "log1p": 2.3978952727983707,
                "log1pPf": 2.3978952727983707,
                "powPI": 1.9275814160560204e-50
            }},
            "pdfViewerEnabled": {"value": True},
            "architecture": {"value": 240},
            "applePay": {"value": -1},
            "privateClickMeasurement": {"value": None},
            "webGlBasics": {"value": self.get_webgl_info()},
            "webGlExtensions": {"value": {
                "contextAttributes": ["alpha=true", "antialias=true", "depth=true", "premultipliedAlpha=true", "preserveDrawingBuffer=false", "stencil=false"],
                "parameters": ["MAX_VERTEX_ATTRIBS=16", "MAX_VERTEX_UNIFORM_VECTORS=254", "MAX_FRAGMENT_UNIFORM_VECTORS=221"],
                "shaderPrecisions": ["VERTEX_SHADER.HIGH_FLOAT=23,127,23", "FRAGMENT_SHADER.HIGH_FLOAT=23,127,23"],
                "extensions": ["ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float"],
                "extensionParameters": [],
                "unsupportedExtensions": []
            }}
        }
        
        return self.components
    
    def components_to_string(self, components):
        """Convert components to string for hashing"""
        result = ""
        sorted_keys = sorted(components.keys())
        
        for key in sorted_keys:
            component = components[key]
            if "error" in component:
                value = "error"
            else:
                value = json.dumps(component["value"], separators=(',', ':'), sort_keys=True)
            
            if result:
                result += "|"
            
            # Fix f-string backslash issue
            escaped_key = key.replace(':', '\\:').replace('|', '\\|')
            result += f"{escaped_key}:{value}"
        
        return result
    
    def hash_components(self, components_string):
        """Hash the components string to create visitor ID"""
        # Use SHA-256 and take first 20 characters (similar to FingerprintJS)
        hash_obj = hashlib.sha256(components_string.encode('utf-8'))
        hash_hex = hash_obj.hexdigest()
        return hash_hex[:20]
    
    def generate_fingerprint(self):
        """Generate a complete fingerprint"""
        components = self.collect_components()
        components_string = self.components_to_string(components)
        visitor_id = self.hash_components(components_string)
        
        return {
            "visitorId": visitor_id,
            "components": components,
            "confidence": 0.995  # High confidence score
        }

def generate_fingerprint():
    """Convenience function to generate a fingerprint"""
    generator = FingerprintGenerator()
    return generator.generate_fingerprint()