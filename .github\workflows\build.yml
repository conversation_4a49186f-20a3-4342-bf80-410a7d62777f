# name: Build and Test

# on:
#   push:
#     branches: [ main, develop ]
#   pull_request:
#     branches: [ main, develop ]
#   workflow_dispatch:  # 允许手动触发

# jobs:
#   build:
#     runs-on: windows-latest
    
#     steps:
#     - name: Checkout code
#       uses: actions/checkout@v4
      
#     - name: Set up Go
#       uses: actions/setup-go@v4
#       with:
#         go-version: '1.21'
        
#     - name: Cache Go modules
#       uses: actions/cache@v3
#       with:
#         path: |
#           ~\AppData\Local\go-build
#           ~\go\pkg\mod
#         key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
#         restore-keys: |
#           ${{ runner.os }}-go-
          
#     - name: Download dependencies
#       run: go mod download
      
#     - name: Verify dependencies
#       run: go mod verify
      
#     - name: Run go vet
#       run: go vet ./...
      
#     - name: Build application
#       run: go build -v -o oh-my-ai2api.exe .
      
#     - name: Test build artifact
#       run: |
#         if (Test-Path oh-my-ai2api.exe) {
#           Write-Host "✅ Build successful - executable created"
#           $fileInfo = Get-Item oh-my-ai2api.exe
#           Write-Host "File size: $($fileInfo.Length) bytes"
#         } else {
#           Write-Host "❌ Build failed - executable not found"
#           exit 1
#         }
        
#     - name: Upload build artifact
#       uses: actions/upload-artifact@v3
#       with:
#         name: oh-my-ai2api-windows-${{ github.sha }}
#         path: oh-my-ai2api.exe
#         retention-days: 7
