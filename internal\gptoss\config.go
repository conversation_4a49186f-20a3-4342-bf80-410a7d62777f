package gptoss

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"time"

	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
)

// GptossConfig Gptoss相关配置
type GptossConfig struct {
	SessionID     string
	Fingerprint   string
	FingerprintData *FingerprintData
	PkID          string
	TargetURL     string
	Models        []Model
}

// Model 模型信息
type Model struct {
	ID          string `json:"id"`
	Object      string `json:"object"`
	Created     int64  `json:"created"`
	OwnedBy     string `json:"owned_by"`
}

// DefaultGptossConfig 创建默认Gptoss配置
func DefaultGptossConfig() *GptossConfig {
	// 生成fingerprint
	fingerprintData := GenerateFingerprint()
	
	return &GptossConfig{
		SessionID:        "",
		Fingerprint:      fingerprintData.VisitorID,
		FingerprintData:  fingerprintData,
		PkID:            generateRandomPkID(),
		TargetURL:        "https://chat-gpt-oss.com/api/message",
		Models: []Model{
			{
				ID:          "gpt-oss-120b",
				Object:      "model",
				Created:     1677610602,
				OwnedBy:     "chat-gpt-oss",
			},
			{
				ID:          "gpt-5-nano",
				Object:      "model",
				Created:     1677610602,
				OwnedBy:     "chat-gpt-oss",
			},
		},
	}
}

// ParseGptossConfig 从配置解析Gptoss设置（使用默认HTTP配置）
func ParseGptossConfig() *GptossConfig {
	return ParseGptossConfigWithHTTP(nil, nil, nil)
}

// ParseGptossConfigWithHTTP 从配置解析Gptoss设置（支持全局HTTP配置）
func ParseGptossConfigWithHTTP(httpConfig *config.HTTPConfig, proxyConfig *config.ProxyConfig, logger *common.Logger) *GptossConfig {
	config := DefaultGptossConfig()

	// 从环境变量获取配置
	config.SessionID = getEnv("GPTOSS_SESSION_ID", "")
	config.Fingerprint = getEnv("GPTOSS_FINGERPRINT", "")
	config.PkID = getEnv("GPTOSS_PKID", "")

	// 如果没有配置fingerprint，自动刷新生成新的指纹
	if config.Fingerprint == "" {
		config.RefreshFingerprint()
	}

	// 如果没有配置PkID，自动刷新生成新的PkID
	if config.PkID == "" {
		config.RefreshPkID()
	}

	// 如果没有配置SessionID，自动获取
	if config.SessionID == "" {
		var sessionID string
		var err error

		// 如果提供了全局HTTP配置，使用它；否则使用默认配置
		if httpConfig != nil || proxyConfig != nil {
			sessionID, err = fetchGuestSessionIDWithConfig(config.PkID, httpConfig, proxyConfig, logger)
		} else {
			sessionID, err = fetchGuestSessionID(config.PkID)
		}

		if err != nil {
			// 如果获取失败，使用默认值
			fmt.Printf("Warning: Failed to fetch guest_session_id: %v\n", err)
			config.SessionID = ""
		} else {
			config.SessionID = sessionID
		}
	}

	return config
}

// RefreshFingerprint 刷新fingerprint
func (c *GptossConfig) RefreshFingerprint() {
	fingerprintData := GenerateFingerprint()
	c.Fingerprint = fingerprintData.VisitorID
	c.FingerprintData = fingerprintData
}

// RefreshSessionID 刷新SessionID（使用默认配置）
func (c *GptossConfig) RefreshSessionID() error {
	sessionID, err := fetchGuestSessionID(c.PkID)
	if err != nil {
		return fmt.Errorf("failed to refresh session ID: %w", err)
	}
	c.SessionID = sessionID
	return nil
}

// RefreshSessionIDWithConfig 使用全局配置刷新SessionID
func (c *GptossConfig) RefreshSessionIDWithConfig(httpConfig *config.HTTPConfig, proxyConfig *config.ProxyConfig, logger *common.Logger) error {
	sessionID, err := fetchGuestSessionIDWithConfig(c.PkID, httpConfig, proxyConfig, logger)
	if err != nil {
		return fmt.Errorf("failed to refresh session ID: %w", err)
	}
	c.SessionID = sessionID
	return nil
}

// generateRandomPkID 生成随机的 _pk_id.48.98b5 值
func generateRandomPkID() string {
	// 生成16位随机十六进制字符串
	hexChars := "0123456789abcdef"
	result := make([]byte, 16)
	
	for i := range result {
		n, _ := rand.Int(rand.Reader, big.NewInt(16))
		result[i] = hexChars[n.Int64()]
	}
	
	// 获取当前时间戳
	timestamp := time.Now().Unix()
	
	return fmt.Sprintf("%s.%d.", string(result), timestamp)
}

// RefreshPkID 刷新 PkID
func (c *GptossConfig) RefreshPkID() {
	c.PkID = generateRandomPkID()
}

// fetchGuestSessionID 获取guest_session_id（使用默认配置）
func fetchGuestSessionID(pkID string) (string, error) {
	// 创建HTTP客户端（这里使用默认配置，因为在配置初始化阶段全局设置可能还不可用）
	// 如果需要支持代理和SSL配置，可以考虑将此函数移到运行时调用
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	return fetchGuestSessionIDWithClient(pkID, client)
}

// fetchGuestSessionIDWithConfig 使用全局配置获取guest_session_id
func fetchGuestSessionIDWithConfig(pkID string, httpConfig *config.HTTPConfig, proxyConfig *config.ProxyConfig, logger *common.Logger) (string, error) {
	// 使用全局HTTP配置创建客户端
	client := common.CreateHTTPClient(httpConfig, proxyConfig, 10*time.Second, logger)
	return fetchGuestSessionIDWithClient(pkID, client)
}

// fetchGuestSessionIDWithClient 使用指定的HTTP客户端获取guest_session_id
func fetchGuestSessionIDWithClient(pkID string, client *http.Client) (string, error) {
	
	// 创建请求
	req, err := http.NewRequest("GET", "https://chat-gpt-oss.com/api/conversation/messages", nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}
	
	// 设置请求头
	req.Header.Set("accept", "application/json, text/plain, */*")
	req.Header.Set("accept-language", "en-US,en;q=0.9")
	req.Header.Set("cache-control", "no-cache")
	req.Header.Set("cookie", fmt.Sprintf("_pk_id.48.98b5=%s; _pk_ses.48.98b5=1", pkID))
	req.Header.Set("dnt", "1")
	req.Header.Set("pragma", "no-cache")
	req.Header.Set("priority", "u=1, i")
	req.Header.Set("referer", "https://chat-gpt-oss.com/")
	req.Header.Set("sec-ch-ua", "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"")
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("sec-ch-ua-platform", "\"Windows\"")
	req.Header.Set("sec-fetch-dest", "empty")
	req.Header.Set("sec-fetch-mode", "cors")
	req.Header.Set("sec-fetch-site", "same-origin")
	req.Header.Set("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36")
	
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	// 读取响应头中的Set-Cookie
	setCookieHeader := resp.Header.Get("Set-Cookie")
	if setCookieHeader == "" {
		return "", fmt.Errorf("no Set-Cookie header found in response")
	}
	
	// 解析Set-Cookie头获取guest_session_id
	return extractGuestSessionID(setCookieHeader)
}

// extractGuestSessionID 从Set-Cookie头中提取guest_session_id
func extractGuestSessionID(setCookieHeader string) (string, error) {
	// 使用正则表达式匹配guest_session_id
	re := regexp.MustCompile(`guest_session_id=([^;]+)`)
	matches := re.FindStringSubmatch(setCookieHeader)
	if len(matches) < 2 {
		return "", fmt.Errorf("guest_session_id not found in Set-Cookie header")
	}
	
	// URL解码session_id
	sessionID, err := url.QueryUnescape(matches[1])
	if err != nil {
		return "", fmt.Errorf("failed to URL decode session ID: %w", err)
	}
	
	return sessionID, nil
}

// getEnv 获取环境变量
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}