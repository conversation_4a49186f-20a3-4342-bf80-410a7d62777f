package common

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ErrorResponse OpenAI兼容的错误响应
type ErrorResponse struct {
	Error ErrorDetail `json:"error"`
}

// ErrorDetail 错误详情
type ErrorDetail struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    int    `json:"code"`
}

// SendError 发送错误响应
func SendError(c *gin.Context, statusCode int, message, errorType string) {
	c.JSON(statusCode, ErrorResponse{
		Error: ErrorDetail{
			Message: message,
			Type:    errorType,
			Code:    statusCode,
		},
	})
}

// SendInternalError 发送内部服务器错误
func SendInternalError(c *gin.Context, message string) {
	SendError(c, http.StatusInternalServerError, message, "internal_server_error")
}

// SendBadRequest 发送请求错误
func SendBadRequest(c *gin.Context, message string) {
	SendError(c, http.StatusBadRequest, message, "invalid_request_error")
}

// SendUnauthorized 发送认证错误
func SendUnauthorized(c *gin.Context, message string) {
	SendError(c, http.StatusUnauthorized, message, "invalid_request_error")
}

// SendServiceUnavailable 发送服务不可用错误
func SendServiceUnavailable(c *gin.Context, message string) {
	SendError(c, http.StatusServiceUnavailable, message, "service_unavailable_error")
}

// SetCORSHeaders 设置CORS头部
func SetCORSHeaders() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}

		c.Next()
	})
}
