package common

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
	"gopkg.in/natefinch/lumberjack.v2"
)

// LogConfigInterface 日志配置接口
type LogConfigInterface interface {
	GetEnableFileLog() bool
	GetLogFilePath() string
	GetMaxFileSize() int
	GetMaxBackups() int
	GetMaxAge() int
	GetCompress() bool
}

// ANSI 颜色代码
const (
	ColorReset  = "\033[0m"
	ColorRed    = "\033[31m"
	ColorGreen  = "\033[32m"
	ColorYellow = "\033[33m"
	ColorBlue   = "\033[34m"
	ColorPurple = "\033[35m"
	ColorCyan   = "\033[36m"
	ColorWhite  = "\033[37m"
	ColorGray   = "\033[90m"
)

// Logger 日志工具
type Logger struct {
	Level      string
	fileLogger *log.Logger
	consoleLogger *log.Logger
	enableFileLog bool
}

// NewLogger 创建新的日志器
func NewLogger(level string) *Logger {
	return NewLoggerWithConfig(level, nil)
}

// NewLoggerWithConfig 创建带配置的日志器
func NewLoggerWithConfig(level string, logConfig LogConfigInterface) *Logger {
	logger := &Logger{
		Level: level,
		enableFileLog: false,
	}

	// 创建控制台日志器
	logger.consoleLogger = log.New(os.Stdout, "", 0)

	// 如果启用文件日志
	if logConfig != nil && logConfig.GetEnableFileLog() {
		logger.enableFileLog = true

		// 确保日志目录存在
		logDir := filepath.Dir(logConfig.GetLogFilePath())
		if err := os.MkdirAll(logDir, 0755); err != nil {
			log.Printf("Failed to create log directory: %v", err)
			logger.enableFileLog = false
		} else {
			// 创建lumberjack日志轮转器
			lumberjackLogger := &lumberjack.Logger{
				Filename:   logConfig.GetLogFilePath(),
				MaxSize:    logConfig.GetMaxFileSize(), // MB
				MaxBackups: logConfig.GetMaxBackups(),
				MaxAge:     logConfig.GetMaxAge(), // days
				Compress:   logConfig.GetCompress(),
			}

			// 创建文件日志器
			logger.fileLogger = log.New(lumberjackLogger, "", 0)

			// 创建多输出日志器（同时输出到控制台和文件）
			multiWriter := io.MultiWriter(os.Stdout, lumberjackLogger)
			logger.consoleLogger = log.New(multiWriter, "", 0)
		}
	}

	return logger
}

// Debug 调试日志
func (l *Logger) Debug(msg string) {
	if l.Level == "DEBUG" {
		timestamp := time.Now().Format(time.RFC3339)
		// 控制台输出（带颜色）
		consoleMsg := fmt.Sprintf("%s[DEBUG]%s %s%s%s - %s", ColorCyan, ColorReset, ColorGray, timestamp, ColorReset, msg)
		// 文件输出（无颜色）
		fileMsg := fmt.Sprintf("[DEBUG] %s - %s", timestamp, msg)

		if l.enableFileLog && l.fileLogger != nil {
			l.fileLogger.Println(fileMsg)
		}
		// 如果没有启用文件日志，使用控制台日志器
		if !l.enableFileLog {
			l.consoleLogger.Println(consoleMsg)
		}
	}
}

// Info 信息日志
func (l *Logger) Info(msg string) {
	if l.Level == "DEBUG" || l.Level == "INFO" {
		timestamp := time.Now().Format(time.RFC3339)
		// 控制台输出（带颜色）
		consoleMsg := fmt.Sprintf("%s[INFO]%s %s%s%s - %s", ColorGreen, ColorReset, ColorGray, timestamp, ColorReset, msg)
		// 文件输出（无颜色）
		fileMsg := fmt.Sprintf("[INFO] %s - %s", timestamp, msg)

		if l.enableFileLog && l.fileLogger != nil {
			l.fileLogger.Println(fileMsg)
		}
		// 如果没有启用文件日志，使用控制台日志器
		if !l.enableFileLog {
			l.consoleLogger.Println(consoleMsg)
		}
	}
}

// Warning 警告日志
func (l *Logger) Warning(msg string) {
	timestamp := time.Now().Format(time.RFC3339)
	// 控制台输出（带颜色）
	consoleMsg := fmt.Sprintf("%s[WARNING]%s %s%s%s - %s", ColorYellow, ColorReset, ColorGray, timestamp, ColorReset, msg)
	// 文件输出（无颜色）
	fileMsg := fmt.Sprintf("[WARNING] %s - %s", timestamp, msg)

	if l.enableFileLog && l.fileLogger != nil {
		l.fileLogger.Println(fileMsg)
	}
	// 如果没有启用文件日志，使用控制台日志器
	if !l.enableFileLog {
		l.consoleLogger.Println(consoleMsg)
	}
}

// Error 错误日志
func (l *Logger) Error(msg string) {
	timestamp := time.Now().Format(time.RFC3339)
	// 控制台输出（带颜色）
	consoleMsg := fmt.Sprintf("%s[ERROR]%s %s%s%s - %s", ColorRed, ColorReset, ColorGray, timestamp, ColorReset, msg)
	// 文件输出（无颜色）
	fileMsg := fmt.Sprintf("[ERROR] %s - %s", timestamp, msg)

	if l.enableFileLog && l.fileLogger != nil {
		l.fileLogger.Println(fileMsg)
	}
	// 如果没有启用文件日志，使用控制台日志器
	if !l.enableFileLog {
		l.consoleLogger.Println(consoleMsg)
	}
}

// DebugJSON 调试日志 - JSON格式
func (l *Logger) DebugJSON(title string, data interface{}) {
	if l.Level == "DEBUG" {
		jsonData, err := json.MarshalIndent(data, "", "  ")
		if err != nil {
			l.Debug(fmt.Sprintf("%s: %v (JSON marshal error: %v)", title, data, err))
			return
		}

		timestamp := time.Now().Format(time.RFC3339)
		// 控制台输出（带颜色）
		consoleMsg := fmt.Sprintf("%s[DEBUG]%s %s%s%s - %s%s%s:\n%s%s%s",
			ColorCyan, ColorReset,
			ColorGray, timestamp, ColorReset,
			ColorBlue, title, ColorReset,
			ColorWhite, string(jsonData), ColorReset)
		// 文件输出（无颜色）
		fileMsg := fmt.Sprintf("[DEBUG] %s - %s:\n%s", timestamp, title, string(jsonData))

		if l.enableFileLog && l.fileLogger != nil {
			l.fileLogger.Println(fileMsg)
		}
		// 如果没有启用文件日志，使用控制台日志器
		if !l.enableFileLog {
			l.consoleLogger.Println(consoleMsg)
		}
	}
}

// DebugHTTPRequest 调试HTTP请求
func (l *Logger) DebugHTTPRequest(req *http.Request) {
	if l.Level == "DEBUG" {
		reqData := map[string]interface{}{
			"method": req.Method,
			"url":    req.URL.String(),
			"headers": req.Header,
		}
		l.DebugJSON("HTTP Request", reqData)
	}
}

// DebugHTTPResponse 调试HTTP响应
func (l *Logger) DebugHTTPResponse(resp *http.Response) {
	if l.Level == "DEBUG" {
		respData := map[string]interface{}{
			"status":     resp.Status,
			"statusCode": resp.StatusCode,
			"headers":    resp.Header,
		}
		l.DebugJSON("HTTP Response", respData)
	}
}

// GenerateUUID 生成UUID
func GenerateUUID() string {
	return uuid.New().String()
}

// GenerateCompletionID 生成聊天完成ID
func GenerateCompletionID() string {
	id := strings.ReplaceAll(GenerateUUID(), "-", "")
	if len(id) > 29 {
		id = id[:29]
	}
	return "chatcmpl-" + id
}

// TransformContent 转换内容，处理thinking标签
func TransformContent(content string, showThinkTags bool) string {
	if content == "" {
		return content
	}

	if !showThinkTags {
		// 移除<details>块（思考内容）
		detailsRegex := regexp.MustCompile(`(?s)<details[^>]*>.*?</details>`)
		content = detailsRegex.ReplaceAllString(content, "")

		// 移除未闭合的<details>块 - 使用更简单的方法
		// 查找<details>标签，然后移除从该位置到字符串末尾的内容
		detailsIndex := strings.Index(content, "<details")
		if detailsIndex != -1 {
			// 检查是否有对应的</details>
			detailsEnd := strings.Index(content[detailsIndex:], "</details>")
			if detailsEnd == -1 {
				// 没有闭合标签，移除从<details>开始的所有内容
				content = content[:detailsIndex]
			}
		}

		content = strings.TrimSpace(content)
	} else {
		// 替换<details>为<think>
		content = regexp.MustCompile(`<details[^>]*>`).ReplaceAllString(content, "<think>")
		content = strings.ReplaceAll(content, "</details>", "</think>")

		// 移除<summary>标签
		summaryRegex := regexp.MustCompile(`(?s)<summary>.*?</summary>`)
		content = summaryRegex.ReplaceAllString(content, "")

		// 如果没有闭合的</think>，添加它
		if strings.Contains(content, "<think>") && !strings.Contains(content, "</think>") {
			thinkStart := strings.Index(content, "<think>")
			if thinkStart != -1 {
				// 寻找可能的答案开始位置（新行后跟大写字母或数字）
				answerRegex := regexp.MustCompile(`\n\s*[A-Z0-9]`)
				matches := answerRegex.FindStringIndex(content[thinkStart:])
				if matches != nil {
					insertPos := thinkStart + matches[0]
					content = content[:insertPos] + "</think>\n" + content[insertPos:]
				} else {
					content += "</think>"
				}
			}
		}
	}

	return strings.TrimSpace(content)
}

// ContentPart 内容部分（用于多模态）
type ContentPart struct {
	Type     string    `json:"type"`
	Text     string    `json:"text,omitempty"`
	ImageURL *ImageURL `json:"image_url,omitempty"`
}

// ImageURL 图片URL
type ImageURL struct {
	URL string `json:"url"`
}

// MessageContent 消息内容处理器
type MessageContent struct {
	Raw json.RawMessage `json:"-"`
}

// UnmarshalJSON 自定义JSON解析
func (mc *MessageContent) UnmarshalJSON(data []byte) error {
	mc.Raw = data
	return nil
}

// MarshalJSON 自定义JSON序列化
func (mc *MessageContent) MarshalJSON() ([]byte, error) {
	if mc.Raw != nil {
		return mc.Raw, nil
	}
	return []byte(`""`), nil
}

// GetTextContent 获取文本内容（处理字符串和数组两种格式）
func (mc *MessageContent) GetTextContent() string {
	if mc.Raw == nil {
		return ""
	}

	// 尝试解析为字符串
	var textContent string
	if err := json.Unmarshal(mc.Raw, &textContent); err == nil {
		return textContent
	}

	// 尝试解析为数组
	var arrayContent []ContentPart
	if err := json.Unmarshal(mc.Raw, &arrayContent); err == nil {
		var textParts []string
		for _, part := range arrayContent {
			if part.Type == "text" {
				textParts = append(textParts, part.Text)
			}
		}
		return strings.Join(textParts, " ")
	}

	return ""
}

// SetTextContent 设置文本内容
func (mc *MessageContent) SetTextContent(text string) {
	data, _ := json.Marshal(text)
	mc.Raw = data
}
