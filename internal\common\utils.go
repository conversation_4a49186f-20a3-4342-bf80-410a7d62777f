package common

import (
	"encoding/json"
	"log"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
)

// Logger 简单的日志工具
type Logger struct {
	Level string
}

// NewLogger 创建新的日志器
func NewLogger(level string) *Logger {
	return &Logger{Level: level}
}

// Debug 调试日志
func (l *Logger) Debug(msg string) {
	if l.Level == "DEBUG" {
		log.Printf("[DEBUG] %s - %s", time.Now().Format(time.RFC3339), msg)
	}
}

// Info 信息日志
func (l *Logger) Info(msg string) {
	if l.Level == "DEBUG" || l.Level == "INFO" {
		log.Printf("[INFO] %s - %s", time.Now().Format(time.RFC3339), msg)
	}
}

// Warning 警告日志
func (l *Logger) Warning(msg string) {
	log.Printf("[WARNING] %s - %s", time.Now().Format(time.RFC3339), msg)
}

// Error 错误日志
func (l *Logger) Error(msg string) {
	log.Printf("[ERROR] %s - %s", time.Now().Format(time.RFC3339), msg)
}

// GenerateUUID 生成UUID
func GenerateUUID() string {
	return uuid.New().String()
}

// GenerateCompletionID 生成聊天完成ID
func GenerateCompletionID() string {
	id := strings.ReplaceAll(GenerateUUID(), "-", "")
	if len(id) > 29 {
		id = id[:29]
	}
	return "chatcmpl-" + id
}

// TransformContent 转换内容，处理thinking标签
func TransformContent(content string, showThinkTags bool) string {
	if content == "" {
		return content
	}

	if !showThinkTags {
		// 移除<details>块（思考内容）
		detailsRegex := regexp.MustCompile(`(?s)<details[^>]*>.*?</details>`)
		content = detailsRegex.ReplaceAllString(content, "")

		// 移除未闭合的<details>块 - 使用更简单的方法
		// 查找<details>标签，然后移除从该位置到字符串末尾的内容
		detailsIndex := strings.Index(content, "<details")
		if detailsIndex != -1 {
			// 检查是否有对应的</details>
			detailsEnd := strings.Index(content[detailsIndex:], "</details>")
			if detailsEnd == -1 {
				// 没有闭合标签，移除从<details>开始的所有内容
				content = content[:detailsIndex]
			}
		}

		content = strings.TrimSpace(content)
	} else {
		// 替换<details>为<think>
		content = regexp.MustCompile(`<details[^>]*>`).ReplaceAllString(content, "<think>")
		content = strings.ReplaceAll(content, "</details>", "</think>")

		// 移除<summary>标签
		summaryRegex := regexp.MustCompile(`(?s)<summary>.*?</summary>`)
		content = summaryRegex.ReplaceAllString(content, "")

		// 如果没有闭合的</think>，添加它
		if strings.Contains(content, "<think>") && !strings.Contains(content, "</think>") {
			thinkStart := strings.Index(content, "<think>")
			if thinkStart != -1 {
				// 寻找可能的答案开始位置（新行后跟大写字母或数字）
				answerRegex := regexp.MustCompile(`\n\s*[A-Z0-9]`)
				matches := answerRegex.FindStringIndex(content[thinkStart:])
				if matches != nil {
					insertPos := thinkStart + matches[0]
					content = content[:insertPos] + "</think>\n" + content[insertPos:]
				} else {
					content += "</think>"
				}
			}
		}
	}

	return strings.TrimSpace(content)
}

// ContentPart 内容部分（用于多模态）
type ContentPart struct {
	Type     string    `json:"type"`
	Text     string    `json:"text,omitempty"`
	ImageURL *ImageURL `json:"image_url,omitempty"`
}

// ImageURL 图片URL
type ImageURL struct {
	URL string `json:"url"`
}

// MessageContent 消息内容处理器
type MessageContent struct {
	Raw json.RawMessage `json:"-"`
}

// UnmarshalJSON 自定义JSON解析
func (mc *MessageContent) UnmarshalJSON(data []byte) error {
	mc.Raw = data
	return nil
}

// MarshalJSON 自定义JSON序列化
func (mc *MessageContent) MarshalJSON() ([]byte, error) {
	if mc.Raw != nil {
		return mc.Raw, nil
	}
	return []byte(`""`), nil
}

// GetTextContent 获取文本内容（处理字符串和数组两种格式）
func (mc *MessageContent) GetTextContent() string {
	if mc.Raw == nil {
		return ""
	}

	// 尝试解析为字符串
	var textContent string
	if err := json.Unmarshal(mc.Raw, &textContent); err == nil {
		return textContent
	}

	// 尝试解析为数组
	var arrayContent []ContentPart
	if err := json.Unmarshal(mc.Raw, &arrayContent); err == nil {
		var textParts []string
		for _, part := range arrayContent {
			if part.Type == "text" {
				textParts = append(textParts, part.Text)
			}
		}
		return strings.Join(textParts, " ")
	}

	return ""
}

// SetTextContent 设置文本内容
func (mc *MessageContent) SetTextContent(text string) {
	data, _ := json.Marshal(text)
	mc.Raw = data
}
