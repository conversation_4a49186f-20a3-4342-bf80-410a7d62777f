package gmi

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"time"

	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
)

// Client GMI API客户端
type Client struct {
	config     *config.GMIConfig
	proxyConfig *config.ProxyConfig
	logger     *common.Logger
	httpClient *http.Client
}

// NewClient 创建新的GMI客户端
func NewClient(gmiConfig *config.GMIConfig, proxyConfig *config.ProxyConfig, logger *common.Logger) *Client {
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 60 * time.Second,
	}

	// 如果启用了代理，配置代理
	if proxyConfig.Enabled && proxyConfig.Host != "" {
		proxyURL := fmt.Sprintf("http://%s:%d", proxyConfig.Host, proxyConfig.Port)
		if parsedURL, err := url.Parse(proxyURL); err == nil {
			client.Transport = &http.Transport{
				Proxy: http.ProxyURL(parsedURL),
			}
			logger.Info(fmt.Sprintf("GMI client configured with proxy: %s", proxyURL))
		}
	}

	return &Client{
		config:      gmiConfig,
		proxyConfig: proxyConfig,
		logger:      logger,
		httpClient:  client,
	}
}

// GetRandomUserAgent 获取随机User-Agent
func (c *Client) GetRandomUserAgent() string {
	if len(c.config.UserAgents) == 0 {
		return "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
	}
	return c.config.UserAgents[rand.Intn(len(c.config.UserAgents))]
}

// GetModels 获取模型列表
func (c *Client) GetModels() (*ModelsResponse, error) {
	c.logger.Debug("Getting GMI models...")

	req, err := http.NewRequest("GET", c.config.ModelsURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+c.config.GMIBearerToken)
	req.Header.Set("User-Agent", c.GetRandomUserAgent())
	req.Header.Set("Accept", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("GMI models API error (%d): %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var modelsResp ModelsResponse
	if err := json.NewDecoder(resp.Body).Decode(&modelsResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	c.logger.Debug(fmt.Sprintf("Retrieved %d models from GMI", len(modelsResp.Data)))
	return &modelsResp, nil
}

// ChatCompletion 聊天完成
func (c *Client) ChatCompletion(request *GMIRequest) (*http.Response, error) {
	c.logger.Debug("Sending chat completion request to GMI...")

	// 序列化请求
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", c.config.TargetURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", c.GetRandomUserAgent())
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Origin", "https://console.gmicloud.ai")
	req.Header.Set("Referer", "https://console.gmicloud.ai/playground/llm/qwen3-coder-480b-a35b-instruct-fp8/1c44de32-1a64-4fd6-959b-273ffefa0a6b?tab=playground")
	req.Header.Set("Sec-Ch-Ua", `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"Windows"`)
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "same-origin")

	// 发送请求
	startTime := time.Now()
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	duration := time.Since(startTime)
	c.logger.Debug(fmt.Sprintf("GMI API call completed in %v", duration))

	if resp.StatusCode != http.StatusOK {
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("GMI API error (%d): %s", resp.StatusCode, string(body))
	}

	return resp, nil
}

// ValidateToken 验证token
func (c *Client) ValidateToken(token string) bool {
	return token == c.config.ValidToken
}
