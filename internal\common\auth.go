package common

import (
	"errors"
	"strings"
)

// AuthResult 认证结果
type AuthResult struct {
	Valid        bool
	UseLocalAuth bool
	UpstreamAuth string
	Error        string
}

// VerifyAuth 验证认证信息
func VerifyAuth(authHeader, localAPIKey string) *AuthResult {
	if authHeader == "" {
		return &AuthResult{
			Valid: false,
			Error: "Authorization header required",
		}
	}

	token := strings.TrimPrefix(authHeader, "Bearer ")
	if token == localAPIKey {
		// 匹配本地API_KEY，使用cookie认证
		return &AuthResult{
			Valid:        true,
			UseLocalAuth: true,
		}
	} else {
		// 不匹配本地API_KEY，透传给上游服务
		return &AuthResult{
			Valid:        true,
			UseLocalAuth: false,
			UpstreamAuth: authHeader,
		}
	}
}

// ValidateModel 验证模型名称
func ValidateModel(requestModel, expectedModel string) error {
	if requestModel != expectedModel {
		return errors.New("model '" + requestModel + "' not supported. Use '" + expectedModel + "'")
	}
	return nil
}
