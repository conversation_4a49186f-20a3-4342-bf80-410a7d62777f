package you

// OpenAIRequest OpenAI API请求结构
// 符合OpenAI Chat Completions API标准格式
type OpenAIRequest struct {
	Messages []Message `json:"messages"` // 聊天消息列表
	Stream   bool      `json:"stream"`   // 是否启用流式响应
	Model    string    `json:"model"`    // 请求的模型名称
}

// Message 聊天消息结构
type Message struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"` // 可以是string或[]ContentPart
}

// ContentPart 消息内容部分（支持多模态）
type ContentPart struct {
	Type     string    `json:"type"`
	Text     string    `json:"text,omitempty"`
	ImageURL *ImageURL `json:"image_url,omitempty"`
}

// ImageURL 图片URL结构
type ImageURL struct {
	URL string `json:"url"`
}

// OpenAIResponse OpenAI API非流式响应结构
type OpenAIResponse struct {
	ID      string         `json:"id"`
	Object  string         `json:"object"`
	Created int64          `json:"created"`
	Model   string         `json:"model"`
	Choices []OpenAIChoice `json:"choices"`
}

// OpenAIChoice OpenAI选择项
type OpenAIChoice struct {
	Message      Message `json:"message"`
	Index        int     `json:"index"`
	FinishReason string  `json:"finish_reason"`
}

// OpenAIStreamResponse OpenAI API流式响应结构
type OpenAIStreamResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
}

// Choice 流式响应选择项
type Choice struct {
	Delta        Delta  `json:"delta"`
	Index        int    `json:"index"`
	FinishReason string `json:"finish_reason"`
}

// Delta 流式响应增量内容
type Delta struct {
	Content string `json:"content"`
}

// ModelResponse 模型列表响应结构
type ModelResponse struct {
	Object string        `json:"object"`
	Data   []ModelDetail `json:"data"`
}

// ModelDetail 模型详细信息
type ModelDetail struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	OwnedBy string `json:"owned_by"`
}

// YouChatResponse You.com聊天响应结构
type YouChatResponse struct {
	YouChatToken string `json:"youChatToken"`
}

// ChatEntry 聊天历史条目
type ChatEntry struct {
	Question string `json:"question"`
	Answer   string `json:"answer"`
}

// NonceResponse Nonce响应结构
type NonceResponse struct {
	Uuid string
}

// UploadResponse 文件上传响应结构
type UploadResponse struct {
	Filename     string `json:"filename"`
	UserFilename string `json:"user_filename"`
}

// TokenCount Token计数结构
type TokenCount struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}