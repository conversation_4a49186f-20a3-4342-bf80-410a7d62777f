package gptoss

import (
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
)

// Handler Gptoss处理器
type Handler struct {
	config      *GptossConfig
	proxy       *Proxy
	logger      *common.Logger
	httpConfig  *config.HTTPConfig
	proxyConfig *config.ProxyConfig
}

// NewHandler 创建新的Gptoss处理器
func NewHandler(config *GptossConfig, httpConfig *config.HTTPConfig, proxyConfig *config.ProxyConfig, logger *common.Logger) *Handler {
	return &Handler{
		config:      config,
		proxy:       NewProxy(config, httpConfig, proxyConfig, logger),
		logger:      logger,
		httpConfig:  httpConfig,
		proxyConfig: proxyConfig,
	}
}

// HandleModels 处理模型列表请求
func (h *Handler) HandleModels(c *gin.Context) {
	response := ModelsResponse{
		Object: "list",
		Data:   h.config.Models,
	}

	c.JSON(http.StatusOK, response)
}

// HandleChatCompletions 处理聊天完成请求
func (h *Handler) HandleChatCompletions(c *gin.Context) {
	var request ChatCompletionRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 验证请求
	if len(request.Messages) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No messages provided"})
		return
	}

	// 转换消息格式
	conversationText := h.convertMessagesToConversation(request.Messages)
	if conversationText == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No valid messages found"})
		return
	}

	// 构建Gptoss请求
	gptossRequest := h.buildGptossRequest(request, conversationText)

	// 处理流式响应
	if request.Stream {
		h.handleStreamResponse(c, gptossRequest, request.Model)
		return
	}

	// 处理普通响应
	h.handleNormalResponse(c, gptossRequest, request.Model, conversationText)
}

// convertMessagesToConversation 转换消息为对话文本
func (h *Handler) convertMessagesToConversation(messages []ChatMessage) string {
	var conversation strings.Builder
	
	for _, msg := range messages {
		switch msg.Role {
		case "system":
			conversation.WriteString(fmt.Sprintf("System: %s\n", msg.Content))
		case "user":
			conversation.WriteString(fmt.Sprintf("User: %s\n", msg.Content))
		case "assistant":
			conversation.WriteString(fmt.Sprintf("Assistant: %s\n", msg.Content))
		}
	}
	
	return strings.TrimSpace(conversation.String())
}

// buildGptossRequest 构建Gptoss请求
func (h *Handler) buildGptossRequest(request ChatCompletionRequest, conversationText string) *GptossRequest {
	gptossRequest := &GptossRequest{
		ConversationID:  nil,
		Model:           "gpt-oss-120b",
		Content:         conversationText,
		ReasoningEffort: request.ReasoningEffort,
	}

	// 如果是gpt-5-nano模型，添加verbosity参数
	if request.Model == "gpt-5-nano" {
		gptossRequest.Verbosity = request.Verbosity
	}

	return gptossRequest
}

// handleStreamResponse 处理流式响应
func (h *Handler) handleStreamResponse(c *gin.Context, gptossRequest *GptossRequest, model string) {
	resp, err := h.proxy.SendStreamRequest(gptossRequest)
	if err != nil {
		h.logger.Error(fmt.Sprintf("Failed to send stream request: %v", err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer resp.Body.Close()

	// 设置流式响应头
	c.Header("Content-Type", "text/plain; charset=utf-8")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Transfer-Encoding", "chunked")

	// 流式发送响应
	streamChan := h.proxy.ParseSSEStream(resp, model)
	
	c.Stream(func(w io.Writer) bool {
		data, ok := <-streamChan
		if !ok {
			return false
		}
		
		_, err := w.Write([]byte(data))
		return err == nil
	})
}

// handleNormalResponse 处理普通响应
func (h *Handler) handleNormalResponse(c *gin.Context, gptossRequest *GptossRequest, model string, conversationText string) {
	// 发送流式请求以获取完整响应
	resp, err := h.proxy.SendStreamRequest(gptossRequest)
	if err != nil {
		h.logger.Error(fmt.Sprintf("Failed to send request: %v", err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer resp.Body.Close()

	// 解析完整响应
	fullContent := h.parseFullResponse(resp)

	// 构建OpenAI格式响应
	response := ChatCompletionResponse{
		ID:      fmt.Sprintf("chatcmpl-%s", common.GenerateUUID()),
		Object:  "chat.completion",
		Created: 1677652288,
		Model:   model,
		Choices: []ChatCompletionChoice{
			{
				Index: 0,
				Message: ChatMessage{
					Role:    "assistant",
					Content: fullContent,
				},
				FinishReason: "stop",
			},
		},
		Usage: Usage{
			PromptTokens:     len(strings.Split(conversationText, " ")),
			CompletionTokens: len(strings.Split(fullContent, " ")),
			TotalTokens:      len(strings.Split(conversationText, " ")) + len(strings.Split(fullContent, " ")),
		},
	}

	c.JSON(http.StatusOK, response)
}

// parseFullResponse 解析完整响应
func (h *Handler) parseFullResponse(resp *http.Response) string {
	scanner := bufio.NewScanner(resp.Body)
	var fullContent strings.Builder
	
	for scanner.Scan() {
		line := scanner.Text()
		
		if strings.HasPrefix(line, "data:") {
			dataContent := strings.TrimPrefix(line, "data:")
			var streamResp GptossStreamResponse
			
			if err := json.Unmarshal([]byte(dataContent), &streamResp); err == nil {
				if streamResp.Content != "" {
					fullContent.WriteString(streamResp.Content)
				}
			}
		}
	}
	
	return fullContent.String()
}

// HandleRefreshFingerprint 处理fingerprint刷新请求
func (h *Handler) HandleRefreshFingerprint(c *gin.Context) {
	h.config.RefreshFingerprint()
	
	c.JSON(http.StatusOK, gin.H{
		"message":        "Fingerprint refreshed successfully",
		"new_fingerprint": h.config.Fingerprint,
		"confidence":     h.config.FingerprintData.Confidence,
	})
}

// HandleRefreshPkID 处理PkID刷新请求
func (h *Handler) HandleRefreshPkID(c *gin.Context) {
	h.config.RefreshPkID()
	
	c.JSON(http.StatusOK, gin.H{
		"message":     "PkID refreshed successfully",
		"new_pkid":    h.config.PkID,
	})
}

// HandleRefreshAll 处理所有标识符刷新请求
func (h *Handler) HandleRefreshAll(c *gin.Context) {
	h.config.RefreshFingerprint()
	h.config.RefreshPkID()
	
	// 尝试刷新SessionID，但不影响整体结果
	sessionIDError := ""
	if err := h.config.RefreshSessionIDWithConfig(h.httpConfig, h.proxyConfig, h.logger); err != nil {
		sessionIDError = fmt.Sprintf(" (SessionID refresh failed: %v)", err)
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message":        "All identifiers refreshed successfully" + sessionIDError,
		"new_fingerprint": h.config.Fingerprint,
		"fingerprint_confidence": h.config.FingerprintData.Confidence,
		"new_pkid":       h.config.PkID,
		"new_session_id": h.config.SessionID,
	})
}

// HandleRefreshSessionID 处理SessionID刷新请求
func (h *Handler) HandleRefreshSessionID(c *gin.Context) {
	if err := h.config.RefreshSessionIDWithConfig(h.httpConfig, h.proxyConfig, h.logger); err != nil {
		h.logger.Error(fmt.Sprintf("Failed to refresh session ID: %v", err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to refresh session ID: %v", err),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message":     "SessionID refreshed successfully",
		"new_session_id": h.config.SessionID,
	})
}

// HandleStatus 处理状态检查请求
func (h *Handler) HandleStatus(c *gin.Context) {
	// 检查SessionID是否为空（可能表示获取失败）
	sessionStatus := "configured"
	if h.config.SessionID == "" {
		sessionStatus = "not_configured"
	}
	
	c.JSON(http.StatusOK, gin.H{
		"status":            "running",
		"session_id":        h.config.SessionID,
		"session_status":    sessionStatus,
		"current_fingerprint": h.config.Fingerprint,
		"fingerprint_confidence": h.config.FingerprintData.Confidence,
		"current_pkid":      h.config.PkID,
		"endpoints": gin.H{
			"chat_completions":   "/v1/chat/completions",
			"models":            "/v1/models",
			"refresh_fingerprint": "/refresh-fingerprint",
			"refresh_pkid":      "/refresh-pkid",
			"refresh_session_id": "/refresh-session-id",
			"refresh_all":       "/refresh-all",
			"status":            "/status",
		},
	})
}