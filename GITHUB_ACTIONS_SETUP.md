# GitHub Actions 权限配置说明

## 问题解决

如果遇到 "Resource not accessible by integration" 错误，这通常是权限配置问题。

## 已修复的配置

### 1. 工作流权限设置

在 `release.yml` 中添加了权限配置：

```yaml
permissions:
  contents: write  # 允许创建 release 和上传文件
```

### 2. 使用现代化的 Action

替换了旧的 actions：
- ❌ `actions/create-release@v1` (已废弃)
- ❌ `actions/upload-release-asset@v1` (已废弃)
- ✅ `softprops/action-gh-release@v1` (推荐)

### 3. 仓库设置检查

确保在 GitHub 仓库设置中：

1. **Actions 权限**：
   - 进入 `Settings` → `Actions` → `General`
   - 确保 "Workflow permissions" 设置为 "Read and write permissions"

2. **Token 权限**：
   - `GITHUB_TOKEN` 会自动提供，无需手动配置
   - 新的权限配置会自动生效

## 测试发布

创建测试标签来验证配置：

```bash
# 创建并推送标签
git tag v0.1.0-test
git push origin v0.1.0-test
```

## 故障排除

如果仍然遇到问题：

1. **检查仓库权限**：确保你有仓库的管理员权限
2. **检查分支保护**：确保没有阻止 Actions 的分支保护规则
3. **查看 Actions 日志**：检查详细的错误信息
4. **重新运行工作流**：有时候重新运行可以解决临时问题

## 权限说明

- `contents: write` - 允许创建 releases、上传文件、修改仓库内容
- `contents: read` - 只允许读取仓库内容（用于构建工作流）

这些权限设置遵循最小权限原则，只授予必要的权限。
