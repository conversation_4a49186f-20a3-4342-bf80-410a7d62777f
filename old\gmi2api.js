const http = require('http');
const https = require('https');
const { URL } = require('url');

// 服务器配置变量
const PORT = process.env.PORT || 5650;
const VALID_TOKEN = 'gmi-free-2-api';
const TARGET_URL = 'https://console.gmicloud.ai/chat';
const MODELS_URL = 'https://api.gmi-serving.com/v1/models';

// 代理配置常量
const PROXY_ENABLED = true;
const PROXY_HOST = '127.0.0.1';
const PROXY_PORT = '7897';
const PROXY_PROTOCOL = process.env.PROXY_PROTOCOL || 'http';
const PROXY_USERNAME = process.env.PROXY_USERNAME || '';
const PROXY_PASSWORD = process.env.PROXY_PASSWORD || '';

// 请求超时配置
const REQUEST_TIMEOUT = 60000; // 60秒超时

// 随机User-Agent列表
const USER_AGENTS = [
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
  'Mozilla/5.0 (Linux; Android 13; SM-S908B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
  'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/114.0',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
];

// 获取随机User-Agent
function getRandomUserAgent() {
  return USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
}

// HTTP请求工具函数
function makeRequest(url, options, data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const lib = isHttps ? https : http;
    
    // 基础请求选项
    const requestOptions = {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: REQUEST_TIMEOUT,
    };

    let req;
    
    // 如果启用了代理
    if (PROXY_ENABLED && PROXY_HOST && PROXY_PORT) {
      // 构建代理请求选项
      Object.assign(requestOptions, {
        hostname: PROXY_HOST,
        port: parseInt(PROXY_PORT) || 8080,
        path: url,
        headers: {
          ...requestOptions.headers,
          Host: urlObj.hostname,
        }
      });

      // 如果代理需要认证
      if (PROXY_USERNAME && PROXY_PASSWORD) {
        const auth = Buffer.from(`${PROXY_USERNAME}:${PROXY_PASSWORD}`).toString('base64');
        requestOptions.headers['Proxy-Authorization'] = `Basic ${auth}`;
      }

      console.log(`使用代理 ${PROXY_PROTOCOL}://${PROXY_HOST}:${PROXY_PORT} 发送请求到 ${url}`);
      req = http.request(requestOptions, (res) => {
        handleResponse(res, resolve, reject);
      });
    } else {
      // 不使用代理的直接请求
      Object.assign(requestOptions, {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
      });

      console.log(`直接发送请求到 ${url}`);
      req = lib.request(requestOptions, (res) => {
        handleResponse(res, resolve, reject);
      });
    }

    req.on('error', (error) => {
      console.error('请求错误:', error);
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    if (data) {
      req.write(data);
    }

    req.end();
  });
}

// 处理HTTP响应
function handleResponse(res, resolve, reject) {
  let responseData = '';
  
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    resolve({
      ok: res.statusCode >= 200 && res.statusCode < 300,
      status: res.statusCode,
      headers: res.headers,
      text: () => Promise.resolve(responseData),
      json: () => {
        try {
          return Promise.resolve(JSON.parse(responseData));
        } catch (error) {
          return Promise.reject(new Error('JSON解析失败'));
        }
      },
      body: responseData,
    });
  });
}

// 调用GMICloud API
async function callGMICloudAPI(targetUrl, payload) {
  try {
    const startTime = Date.now();
    const userAgent = getRandomUserAgent();

    const gmiCloudResponse = await makeRequest(targetUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': userAgent,
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Origin': 'https://console.gmicloud.ai',
        'Referer': 'https://console.gmicloud.ai/playground/llm/qwen3-coder-480b-a35b-instruct-fp8/1c44de32-1a64-4fd6-959b-273ffefa0a6b?tab=playground',
        'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
      },
    }, JSON.stringify(payload));

    if (!gmiCloudResponse.ok) {
      throw new Error(
        `GMICloud API Error (${gmiCloudResponse.status}): ${gmiCloudResponse.body || 'Unknown error'}`
      );
    }

    console.log(`API call success, took ${Date.now() - startTime}ms`);
    return gmiCloudResponse;
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
}

// 获取GMI Cloud模型列表
async function getGMICloudModels() {
  try {
    const modelsResponse = await makeRequest(MODELS_URL, {
      method: 'GET',
      headers: {
        Authorization:
          'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImNhNGNkNGU1LTMyY2YtNDQ5OC1hNDZiLTFiYjFmMzI3NTUzMiIsInNjb3BlIjoiaWVfbW9kZWwiLCJjbGllbnRJZCI6IjAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCJ9.TTdQWMVpyx55Zb0oWqWcny1aYAl7yc_ctNmIphkkBfw',
        'User-Agent': getRandomUserAgent(),
      },
    });

    if (!modelsResponse.ok) {
      const error = await modelsResponse.text();
      throw new Error(
        `GMICloud Models API Error (${modelsResponse.status}): ${error || 'Unknown error'}`
      );
    }

    const modelsData = await modelsResponse.json();
    return modelsData;
  } catch (error) {
    console.error('Models API call failed:', error);
    throw error;
  }
}

// 处理OpenAI格式的响应
function createOpenAIResponse(requestBody, gmiCloudResponse, isStreaming) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (isStreaming) {
    // 流式响应处理
    return {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/event-stream',
      },
      body: gmiCloudResponse.body,
    };
  }

  const gmiCloudResult = JSON.parse(gmiCloudResponse.body);

  const response = {
    id: `chatcmpl-${Math.random().toString(36).substring(2, 12)}`,
    object: 'chat.completion',
    created: Math.floor(Date.now() / 1000),
    model: requestBody.model || 'Qwen3-Coder-480B-A35B-Instruct-FP8',
    choices: [
      {
        index: 0,
        message: {
          role: 'assistant',
          content:
            gmiCloudResult.choices?.[0]?.message?.content ||
            gmiCloudResult.result ||
            '',
        },
        finish_reason: gmiCloudResult.choices?.[0]?.finish_reason || 'stop',
      },
    ],
    usage: gmiCloudResult.usage || {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0,
    },
  };

  return {
    status: 200,
    headers: {
      ...corsHeaders,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(response),
  };
}


// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
  const targetUrl = TARGET_URL;
  const url = new URL(req.url, `http://${req.headers.host}`);
  const pathname = url.pathname;

  // CORS configuration
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  // 1. 统一处理所有 OPTIONS 预检
  if (req.method === 'OPTIONS') {
    res.writeHead(204, corsHeaders);
    res.end();
    return;
  }

  // 2. 只处理特定路径
  if (pathname !== '/v1/chat/completions' && pathname !== '/v1/models') {
    res.writeHead(404, {
      ...corsHeaders,
      'Content-Type': 'text/plain',
    });
    res.end('Not Found');
    return;
  }

  // 3. 处理模型列表请求
  if (pathname === '/v1/models') {
    if (req.method !== 'GET') {
      res.writeHead(405, {
        ...corsHeaders,
        'Content-Type': 'text/plain',
      });
      res.end('Method Not Allowed');
      return;
    }

    // Token 验证
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.writeHead(401, {
        ...corsHeaders,
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify({
        error: {
          message: 'Missing or invalid Authorization header',
          type: 'authentication_error',
        },
      }));
      return;
    }

    const token = authHeader.substring(7); // 去掉 "Bearer "
    if (token !== VALID_TOKEN) {
      res.writeHead(401, {
        ...corsHeaders,
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify({
        error: {
          message: 'Invalid token',
          type: 'authentication_error',
        },
      }));
      return;
    }

    try {
      const modelsData = await getGMICloudModels();
      res.writeHead(200, {
        ...corsHeaders,
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify(modelsData));
    } catch (error) {
      console.error('Models API call failed:', error);
      res.writeHead(400, {
        ...corsHeaders,
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify({
        error: {
          message: error.message,
          type: 'invalid_request_error',
        },
      }));
    }
    return;
  }

  // 4. 只允许 POST 请求（/v1/chat/completions）
  if (req.method !== 'POST') {
    res.writeHead(405, {
      ...corsHeaders,
      'Content-Type': 'text/plain',
    });
    res.end('Method Not Allowed');
    return;
  }

  // Token 验证
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    res.writeHead(401, {
      ...corsHeaders,
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify({
      error: {
        message: 'Missing or invalid Authorization header',
        type: 'authentication_error',
      },
    }));
    return;
  }

  const token = authHeader.substring(7); // 去掉 "Bearer "
  if (token !== VALID_TOKEN) {
    res.writeHead(401, {
      ...corsHeaders,
      'Content-Type': 'application/json',
    });
    res.end(JSON.stringify({
      error: {
        message: 'Invalid token',
        type: 'authentication_error',
      },
    }));
    return;
  }

  // 读取请求体
  let requestBody = '';
  req.on('data', (chunk) => {
    requestBody += chunk;
  });

  req.on('end', async () => {
    try {
      const parsedBody = JSON.parse(requestBody);
      const isStreaming = parsedBody.stream === true;

      // 构建GMICloud API请求负载
      const payload = {
        temperature: parsedBody.temperature ?? 0.5,
        max_tokens: parsedBody.max_tokens ?? 4096,
        top_p: parsedBody.top_p ?? 0.95,
        stream: isStreaming,
        messages: parsedBody.messages,
        model: parsedBody.model || 'Qwen3-Coder-480B-A35B-Instruct-FP8',
      };

      // 调用 GMICloud API
      const gmiCloudResponse = await callGMICloudAPI(targetUrl, payload);

      // 返回 OpenAI 格式响应
      const openAIResponse = createOpenAIResponse(
        parsedBody,
        gmiCloudResponse,
        isStreaming
      );

      res.writeHead(openAIResponse.status, openAIResponse.headers);
      res.end(openAIResponse.body);
    } catch (error) {
      console.error('Request processing failed:', error);
      res.writeHead(400, {
        ...corsHeaders,
        'Content-Type': 'application/json',
      });
      res.end(JSON.stringify({
        error: {
          message: error.message,
          type: 'invalid_request_error',
        },
      }));
    }
  });
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`GMICloud API 代理服务器运行在端口 ${PORT}`);
  console.log(`访问地址: http://localhost:${PORT}`);
  console.log(`支持的端点:`);
  console.log(`  - POST /v1/chat/completions - 聊天完成`);
  console.log(`  - GET /v1/models - 获取模型列表`);
  console.log(`认证方式: Bearer ${VALID_TOKEN}`);
  
  // 显示代理配置状态
  if (PROXY_ENABLED && PROXY_HOST && PROXY_PORT) {
    console.log(`代理状态: 已启用`);
    console.log(`代理地址: ${PROXY_PROTOCOL}://${PROXY_HOST}:${PROXY_PORT}`);
    if (PROXY_USERNAME) {
      console.log(`代理认证: 已配置用户名 ${PROXY_USERNAME}`);
    } else {
      console.log(`代理认证: 无`);
    }
  } else {
    console.log(`代理状态: 未启用`);
    console.log(`如需启用代理，请设置环境变量:`);
    console.log(`  PROXY_ENABLED=true`);
    console.log(`  PROXY_HOST=your-proxy-host`);
    console.log(`  PROXY_PORT=your-proxy-port`);
    console.log(`  PROXY_PROTOCOL=http (可选，默认为http)`);
    console.log(`  PROXY_USERNAME=your-username (可选)`);
    console.log(`  PROXY_PASSWORD=your-password (可选)`);
  }
});

server.on('error', (error) => {
  console.error('服务器错误:', error);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到 SIGINT 信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});