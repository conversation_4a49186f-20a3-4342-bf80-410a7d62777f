// Package you 提供You.com API的代理服务
// 该模块将OpenAI格式的请求转换为You.com API格式，支持多模态输入和流式响应
package you

import (
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"oh-my-ai2api/internal/common"
	"oh-my-ai2api/internal/config"
)

// Handler You.com API处理器
type Handler struct {
	config      *config.YouConfig
	proxyConfig *config.ProxyConfig
	logger      *common.Logger
	httpClient  *http.Client
}

// NewHandler 创建新的处理器
func NewHandler(config *config.YouConfig, proxyConfig *config.ProxyConfig, httpConfig *config.HTTPConfig, logger *common.Logger) *Handler {
	// 使用通用HTTP客户端创建函数
	client := common.CreateHTTPClient(httpConfig, proxyConfig, 60*time.Second, logger)

	return &Handler{
		config:      config,
		proxyConfig: proxyConfig,
		logger:      logger,
		httpClient:  client,
	}
}

// HandleModels 处理模型列表请求
func (h *Handler) HandleModels(c *gin.Context) {
	h.logger.Debug("Handling models request")

	models := make([]ModelDetail, 0, len(modelMap))
	created := time.Now().Unix()

	// 添加标准模型
	for modelID := range modelMap {
		models = append(models, ModelDetail{
			ID:      modelID,
			Object:  "model",
			Created: created,
			OwnedBy: "organization-owner",
		})
	}

	// 添加Agent模型
	for _, agentID := range h.config.AgentModelIDs {
		models = append(models, ModelDetail{
			ID:      agentID,
			Object:  "model",
			Created: created,
			OwnedBy: "organization-owner",
		})
	}

	response := ModelResponse{
		Object: "list",
		Data:   models,
	}

	c.JSON(http.StatusOK, response)
}

// HandleChatCompletions 处理聊天完成请求
func (h *Handler) HandleChatCompletions(c *gin.Context) {
	h.logger.Debug("Handling chat completions request")

	// 验证Authorization头
	authHeader := c.GetHeader("Authorization")
	if !strings.HasPrefix(authHeader, "Bearer ") {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing or invalid authorization header"})
		return
	}

	dsToken := strings.TrimPrefix(authHeader, "Bearer ")
	if dsToken == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing DS token"})
		return
	}

	// 解析请求体
	var req OpenAIRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	h.logger.Debug(fmt.Sprintf("Received chat request: model=%s, stream=%v, messages=%d", req.Model, req.Stream, len(req.Messages)))

	// 处理请求
	if req.Stream {
		h.handleStreamingResponse(c, req, dsToken)
	} else {
		h.handleNonStreamingResponse(c, req, dsToken)
	}
}

// handleNonStreamingResponse 处理非流式响应
func (h *Handler) handleNonStreamingResponse(c *gin.Context, req OpenAIRequest, dsToken string) {
	youReq, err := h.buildYouRequest(req, dsToken)
	if err != nil {
		h.logger.Error(fmt.Sprintf("Failed to build You.com request: %v", err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to build request"})
		return
	}

	h.logger.DebugHTTPRequest(youReq)

	// 使用配置的HTTP客户端
	client := h.httpClient
	resp, err := client.Do(youReq)
	if err != nil {
		h.logger.Error(fmt.Sprintf("Failed to send request to You.com: %v", err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send request"})
		return
	}
	defer resp.Body.Close()

	h.logger.DebugHTTPResponse(resp)

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		h.logger.Error(fmt.Sprintf("You.com API error: status=%d, response=%s", resp.StatusCode, string(body)))
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("API returned status %d", resp.StatusCode)})
		return
	}

	// 读取完整响应
	var fullResponse strings.Builder
	scanner := bufio.NewScanner(resp.Body)
	lineCount := 0
	tokenCount := 0

	h.logger.Debug("Starting to parse You.com API response stream")

	for scanner.Scan() {
		line := scanner.Text()
		lineCount++
		h.logger.Debug(fmt.Sprintf("Response line %d: %s", lineCount, line))

		if strings.HasPrefix(line, "event: youChatToken") {
			h.logger.Debug("Found youChatToken event")
			if !scanner.Scan() {
				h.logger.Error("Unexpected end of stream after youChatToken event")
				break
			}
			data := scanner.Text()
			lineCount++
			h.logger.Debug(fmt.Sprintf("Response line %d (data): %s", lineCount, data))

			if strings.HasPrefix(data, "data: ") {
				jsonData := strings.TrimPrefix(data, "data: ")
				h.logger.Debug(fmt.Sprintf("Parsing JSON data: %s", jsonData))

				var token YouChatResponse
				if err := json.Unmarshal([]byte(jsonData), &token); err == nil {
					tokenCount++
					h.logger.Debug(fmt.Sprintf("Token %d: '%s'", tokenCount, token.YouChatToken))
					fullResponse.WriteString(token.YouChatToken)
				} else {
					h.logger.Error(fmt.Sprintf("Failed to unmarshal youChatToken: %v, data: %s", err, jsonData))
				}
			}
		}
	}

	if err := scanner.Err(); err != nil {
		h.logger.Error(fmt.Sprintf("Error reading response stream: %v", err))
	}

	finalContent := fullResponse.String()

	// 使用JSON格式输出解析统计信息
	parseStats := map[string]interface{}{
		"total_lines":    lineCount,
		"tokens_found":   tokenCount,
		"content_length": len(finalContent),
		"final_content":  finalContent,
	}
	h.logger.DebugJSON("Response Parse Results", parseStats)

	// 构建OpenAI格式响应
	openAIResp := OpenAIResponse{
		ID:      "chatcmpl-" + fmt.Sprintf("%d", time.Now().Unix()),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   req.Model,
		Choices: []OpenAIChoice{
			{
				Message: Message{
					Role:    "assistant",
					Content: finalContent,
				},
				Index:        0,
				FinishReason: "stop",
			},
		},
	}

	// 使用JSON格式输出最终响应
	h.logger.DebugJSON("Final OpenAI Response", openAIResp)
	c.JSON(http.StatusOK, openAIResp)
}

// handleStreamingResponse 处理流式响应
func (h *Handler) handleStreamingResponse(c *gin.Context, req OpenAIRequest, dsToken string) {
	youReq, err := h.buildYouRequest(req, dsToken)
	if err != nil {
		h.logger.Error(fmt.Sprintf("Failed to build You.com request: %v", err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to build request"})
		return
	}

	h.logger.DebugHTTPRequest(youReq)

	// 使用配置的HTTP客户端
	client := h.httpClient
	resp, err := client.Do(youReq)
	if err != nil {
		h.logger.Error(fmt.Sprintf("Failed to send request to You.com: %v", err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send request"})
		return
	}
	defer resp.Body.Close()

	h.logger.DebugHTTPResponse(resp)

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		h.logger.Error(fmt.Sprintf("You.com API error: status=%d, response=%s", resp.StatusCode, string(body)))
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("API returned status %d", resp.StatusCode)})
		return
	}

	// 设置流式响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")

	h.logger.Debug("Starting to parse streaming response from You.com API")
	scanner := bufio.NewScanner(resp.Body)
	lineCount := 0
	tokenCount := 0

	for scanner.Scan() {
		line := scanner.Text()
		lineCount++
		h.logger.Debug(fmt.Sprintf("Streaming line %d: %s", lineCount, line))

		if strings.HasPrefix(line, "event: youChatToken") {
			h.logger.Debug("Found youChatToken event in stream")
			if !scanner.Scan() {
				h.logger.Error("Unexpected end of stream after youChatToken event")
				break
			}
			data := scanner.Text()
			lineCount++
			h.logger.Debug(fmt.Sprintf("Streaming line %d (data): %s", lineCount, data))

			if strings.HasPrefix(data, "data: ") {
				jsonData := strings.TrimPrefix(data, "data: ")
				h.logger.Debug(fmt.Sprintf("Parsing streaming JSON data: %s", jsonData))

				var token YouChatResponse
				if err := json.Unmarshal([]byte(jsonData), &token); err == nil {
					tokenCount++
					h.logger.Debug(fmt.Sprintf("Streaming token %d: '%s'", tokenCount, token.YouChatToken))
					openAIResp := OpenAIStreamResponse{
						ID:      "chatcmpl-" + fmt.Sprintf("%d", time.Now().Unix()),
						Object:  "chat.completion.chunk",
						Created: time.Now().Unix(),
						Model:   req.Model,
						Choices: []Choice{
							{
								Delta: Delta{
									Content: token.YouChatToken,
								},
								Index:        0,
								FinishReason: "",
							},
						},
					}

					respBytes, err := json.Marshal(openAIResp)
					if err != nil {
						h.logger.Error(fmt.Sprintf("Failed to marshal stream response: %v", err))
						continue
					}
					
					if _, err := c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", string(respBytes)))); err != nil {
						h.logger.Error(fmt.Sprintf("Failed to write stream response: %v", err))
						break
					}
					c.Writer.Flush()
				} else {
					h.logger.Error(fmt.Sprintf("Failed to unmarshal youChatToken in stream: %v, data: %s", err, jsonData))
				}
			}
		}
	}

	if err := scanner.Err(); err != nil {
		h.logger.Error(fmt.Sprintf("Error reading stream response: %v", err))
	}

	// 使用JSON格式输出流式解析统计信息
	streamStats := map[string]interface{}{
		"total_lines":  lineCount,
		"tokens_sent":  tokenCount,
	}
	h.logger.DebugJSON("Streaming Parse Results", streamStats)

	// 发送结束标记
	endResp := OpenAIStreamResponse{
		ID:      "chatcmpl-" + fmt.Sprintf("%d", time.Now().Unix()),
		Object:  "chat.completion.chunk",
		Created: time.Now().Unix(),
		Model:   req.Model,
		Choices: []Choice{
			{
				Delta: Delta{
					Content: "",
				},
				Index:        0,
				FinishReason: "stop",
			},
		},
	}

	respBytes, _ := json.Marshal(endResp)
	c.Data(http.StatusOK, "text/event-stream", []byte(fmt.Sprintf("data: %s\n\n", string(respBytes))))
	c.Writer.Flush()
}