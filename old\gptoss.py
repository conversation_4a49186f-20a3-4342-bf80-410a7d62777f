# proxy-server.py
from flask import Flask, request, jsonify, Response
import requests
import json
import uuid
import re
from fingerprint_generator import generate_fingerprint

app = Flask(__name__)

SESSION_ID = "" # 用network抓cookie裡面的session_id

fingerprint_data = generate_fingerprint()
FINGERPRINT = fingerprint_data['visitorId']
print(f"Generated fingerprint: {FINGERPRINT}")
print(f"Confidence: {fingerprint_data['confidence']}")

def refresh_fingerprint():
    global FINGERPRINT
    fingerprint_data = generate_fingerprint()
    FINGERPRINT = fingerprint_data['visitorId']
    print(f"Refreshed fingerprint: {FINGERPRINT}")
    return FINGERPRINT

def parse_sse_stream(response, model):
    content_buffer = ""
    message_id = str(uuid.uuid4())
    
    for line in response.iter_lines(decode_unicode=True):
        if not line:
            continue
            
        if line.startswith('event:message'):
            continue
        elif line.startswith('data:'):
            try:
                data_content = line[5:] 
                data = json.loads(data_content)
                
                if 'content' in data:
                    chunk_content = data['content']
                    content_buffer += chunk_content
                    
                    chunk = {
                        "id": f"chatcmpl-{message_id}",
                        "object": "chat.completion.chunk",
                        "created": 1677652288,
                        "model": model,
                        "choices": [{
                            "index": 0,
                            "delta": {
                                "content": chunk_content
                            },
                            "finish_reason": None
                        }]
                    }
                    yield f"data: {json.dumps(chunk)}\n\n"
                    
            except json.JSONDecodeError:
                continue
        elif line.startswith('event:summary'):
            final_chunk = {
                "id": f"chatcmpl-{message_id}",
                "object": "chat.completion.chunk",
                "created": 1677652288,
                "model": model,
                "choices": [{
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }]
            }
            yield f"data: {json.dumps(final_chunk)}\n\n"
            yield "data: [DONE]\n\n"

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    try:
        data = request.json
        
        messages = data.get('messages', [])
        if not messages:
            return jsonify({"error": "No messages provided"}), 400
            
        conversation_text = ""
        for msg in messages:
            role = msg.get('role', '')
            content = msg.get('content', '')
            if role == 'system':
                conversation_text += f"System: {content}\n"
            elif role == 'user':
                conversation_text += f"User: {content}\n"
            elif role == 'assistant':
                conversation_text += f"Assistant: {content}\n"
        
        conversation_text = conversation_text.rstrip()
                
        if not conversation_text:
            return jsonify({"error": "No valid messages found"}), 400
        
        headers = {
            'accept': 'text/event-stream',
            'accept-language': 'zh-TW,zh;q=0.8',
            'content-type': 'application/json',
            'cookie': f'guest_session_id={SESSION_ID}',
            'origin': 'https://chat-gpt-oss.com',
            'referer': 'https://chat-gpt-oss.com/',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-fingerprint': FINGERPRINT
        }
        
        reasoning_effort = data.get('reasoning_effort', 'low')
        verbosity = data.get('verbosity', 'low')
        model = data.get('model', 'gpt-oss-120b')
        
        payload = {
            "conversation_id": None,
            "model": "gpt-oss-120b",
            "content": conversation_text,
            "reasoning_effort": reasoning_effort
        }
        
        if model == 'gpt-5-nano':
            payload["verbosity"] = verbosity
        

        stream = data.get('stream', False)
        
        if stream:
            response = requests.post(
                'https://chat-gpt-oss.com/api/message',
                headers=headers,
                json=payload,
                stream=True
            )
            
            def generate():
                yield from parse_sse_stream(response, model)
                
            return Response(generate(), mimetype='text/plain')
        else:
            response = requests.post(
                'https://chat-gpt-oss.com/api/message',
                headers=headers,
                json=payload,
                stream=True
            )
            
            full_content = ""
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('data:'):
                    try:
                        data_content = line[5:]
                        data = json.loads(data_content)
                        if 'content' in data:
                            full_content += data['content']
                    except json.JSONDecodeError:
                        continue
            
            openai_response = {
                "id": f"chatcmpl-{uuid.uuid4()}",
                "object": "chat.completion",
                "created": 1677652288,
                "model": model,
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": full_content
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": len(conversation_text.split()),
                    "completion_tokens": len(full_content.split()),
                    "total_tokens": len(conversation_text.split()) + len(full_content.split())
                }
            }
            
            return jsonify(openai_response)
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/v1/models', methods=['GET'])
def list_models():
    return jsonify({
        "object": "list",
        "data": [
            {
                "id": "gpt-oss-120b",
                "object": "model", 
                "created": 1677610602,
                "owned_by": "chat-gpt-oss"
            },
            {
                "id": "gpt-5-nano",
                "object": "model", 
                "created": 1677610602,
                "owned_by": "chat-gpt-oss"
            }
        ]
    })

@app.route('/refresh-fingerprint', methods=['POST', 'GET'])
def refresh_fingerprint_endpoint():
    """Endpoint to manually refresh the fingerprint"""
    new_fingerprint = refresh_fingerprint()
    return jsonify({
        "message": "Fingerprint refreshed successfully",
        "new_fingerprint": new_fingerprint
    })

@app.route('/status', methods=['GET'])
def status():
    return jsonify({
        "status": "running",
        "session_id": SESSION_ID,
        "current_fingerprint": FINGERPRINT,
        "endpoints": {
            "chat_completions": "/v1/chat/completions",
            "models": "/v1/models",
            "refresh_fingerprint": "/refresh-fingerprint",
            "status": "/status"
        }
    })

if __name__ == '__main__':
    print("Base_URL: http://localhost:8080")
    print("OPENAI_API_BASE=http://localhost:8080/v1")
    print("OPENAI_API_KEY=random")
    app.run(host='0.0.0.0', port=8080, debug=True)